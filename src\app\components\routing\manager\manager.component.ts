import { Component, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { Area } from '../../../../models/mapsys1';
import { MapService } from '../../../../app/services/map.service';
import { AreaService } from '../../../../app/services/area.service';
import { ImageRedimensionService } from '../../../../app/services/image-redimension.service';
import { EditorService } from '../../../../app/services/editor.service';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AppService } from '../../../../app/services/app.service';
import { Map } from '../../../../models/mapsys1';
import { highlightElement } from '../../../../custom/others';
import { MapPointsService } from '../../../../app/services/map-points.service';
import { DataService } from '../../../../app/services/data.service';
import { DevicesService } from '../../../../app/services/devices.service';
import { ItemService } from '../../../../app/services/item.service';
import { EventService } from '../../../../app/services/event.service';
import { ModalService } from '../../../../app/services/modal.service';

/**
 * Componente gerenciador de mapas
 * Responsável por exibir, criar, editar e remover mapas do sistema
 */
@Component({
  selector: 'app-manager',
  templateUrl: './manager.component.html',
    styleUrls: ['./manager.component.scss'],
})
export class ManagerComponent implements OnInit, AfterViewInit {
  /** Lista de áreas disponíveis */
  areas: Area[];
  /** Inscrição para mudanças de arquivo carregado */
  loadedFileSubscription: Subscription;

  /** Lista de mapas */
  maps: Map[];
  /** Pontos do mapa */
  mapPoints
  /** Pontos de texto */
  textPoints

  /**
   * Construtor do componente gerenciador
   * Injeta todos os serviços necessários para gerenciamento de mapas
   */
  constructor(
    private _router: Router,
    private _mapService: MapService,
    private _dataService: DataService,
    private _mapPointsService: MapPointsService,
    private _areaService: AreaService,
    private _appService: AppService,
    private _editorService: EditorService,
    private _itemService : ItemService,
    private _eventService : EventService,
    private _modalService : ModalService,
    private _imageRedimensionService : ImageRedimensionService,
    private _devicesService : DevicesService
  ) {

  }

  /**
   * Inicializa o componente
   * Carrega dados das áreas, mapas e pontos
   * Redireciona para preferências se DSA não foi carregado
   */
  ngOnInit(): void {
    this.areas = this._areaService.data;
    if (!this._appService.data.DSA_lastLoadedTime) {
      this._router.navigate(['preferences']);
    }

    this.maps = this._mapService.data;
    this.mapPoints = this._mapPointsService.data;
   // this.screenDraws = this._screenDrawsService.data; // Comentado temporariamente

  }

  /**
   * Executa após a inicialização da view
   * Destaca o último mapa modificado se existir
   */
  ngAfterViewInit(): void {
    if (this._mapService.lastModifiedId) {
      highlightElement(
        this._mapService.lastModifiedId,
        1,
        false,
        undefined,
        true
      );
    }
  }

  /**
   * Função de rastreamento para otimização do *ngFor
   * @param index Índice do item
   * @param area Área sendo rastreada
   * @returns Índice para rastreamento
   */
  trackByIndex(index: number, area: Area): any {
    return index;
  }

  /**
   * Edita um mapa específico
   * @param mapId ID do mapa a ser editado
   */
  editMap(mapId: string): void {
    this._mapService.lastModifiedId = mapId;
    this._router.navigate(['map-editor'], { fragment: mapId });
  }

  /**
   * Solicita confirmação para remover um mapa
   * @param map Mapa a ser removido
   */
  promptRemoveMap(map: Map): void {
    this._mapService.promptRemove(map);
  }

  /**
   * Cria um novo mapa para uma área específica
   * @param areaId ID da área onde criar o mapa
   */
  newAddMap(areaId: string)
  {
    this._editorService.newMap(areaId);
  }
}
