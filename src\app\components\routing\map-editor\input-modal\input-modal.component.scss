/* Modal styles */
.map-ratio-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.map-ratio-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 550px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s;
}

.map-ratio-modal-header {
  display: flex;
  justify-content: center;
  padding: 30px;
}

.map-ratio-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #777;
}

label {
  font-weight: 550 !important;
}

.map-ratio-modal-body {
  padding-top: 20px;
  padding-left: 30px;
  padding-right: 30px;
  display: flex
}

.input-group {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;

}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.input-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.map-ratio-modal-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: center;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.b-input-left {
  border-radius: 7px 0 0 7px !important;
}

  .b-input-right {
    border-radius: 0 7px 7px 0 !important;
  }

  .b-input-left:focus, .b-input-right:focus {
    border: 1px solid #b4dbed;
    outline: none;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.06), 0 0 0 2px rgba(100,150,200,.5);
  }