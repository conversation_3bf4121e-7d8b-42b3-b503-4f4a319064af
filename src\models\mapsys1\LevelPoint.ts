import { AnchorPoint } from './AnchorPoint';
import { Ratio } from './ScreenProperties';

/**
 * Classe que representa um ponto de nível no mapa
 * Contém posição principal e posições de controle opcionais para curvas
 */
export class LevelPoint {
  /**
   * Construtor da classe LevelPoint
   * @param position Posição principal do ponto
   * @param controlPosition Array de posições de controle para curvas (opcional)
   */
  constructor(
    public position: AnchorPoint,
    public controlPosition? : AnchorPoint[]
  ){}
}
