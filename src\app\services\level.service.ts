import { Injectable } from '@angular/core';
import { StaticService } from '../../templates/StaticService';
import { Level } from '../../models/mapsys1';

/**
 * Serviço para gerenciamento de níveis
 * Responsável por operações CRUD em níveis do sistema
 */
@Injectable({
  providedIn: 'root'
})
export class LevelService extends StaticService<Level>
{
  /**
   * Construtor do serviço de níveis
   * Inicializa o serviço estático com configurações específicas para níveis
   */
  constructor()
  {
    super(Level.deepLoad, 'Level');
  }
}