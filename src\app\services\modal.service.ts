import { Injectable } from '@angular/core';
import { ItemEvent } from '../../models/mapsys1';

/**
 * Serviço para gerenciamento de modais
 * Controla a exibição e estado dos modais do sistema
 */
@Injectable({
  providedIn: 'root'
})
export class ModalService {

  /** Flag que controla se o modal pode ser controlado */
  canControlModal : boolean = false;
  /** Array de eventos de itens para exibição no modal */
  itemEvent : ItemEvent[] = [];

  /**
   * Construtor do serviço de modal
   */
  constructor() { }
}
