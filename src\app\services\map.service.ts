import { Injectable } from '@angular/core';
import { Map, LevelPoint, Ratio, ASPECT_RATIO_PRESETS } from '../../models/mapsys1';
import { EditableService } from '../../templates/EditableService';

/**
 * Serviço para gerenciamento de mapas
 * Responsável por criar, editar e gerenciar mapas do sistema
 */
@Injectable({
  providedIn: 'root',
})
export class MapService extends EditableService<Map>
{
  /**
   * Cria um novo mapa com pontos
   * @param areaId ID da área associada ao mapa
   * @param aspectRatio Proporção de aspecto do mapa
   * @returns Novo mapa criado
   */
   public promptCreateNewMapPoints(areaId: string, aspectRatio: Ratio): Map
  {
    return new Map(areaId, this.nextIndex(), aspectRatio || ASPECT_RATIO_PRESETS[0]);
  }

  /**
   * Construtor do serviço de mapas
   * Inicializa o serviço editável com configurações específicas para mapas
   */
  constructor()
  {
    super(Map.deepLoad, 'Map');
  }
}
