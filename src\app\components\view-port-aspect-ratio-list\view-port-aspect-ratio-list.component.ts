import { UserSettingsService } from '../../../app/services/user-settings.service';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Ratio } from '../../../models/mapsys1';
import { Alert } from '../../../custom/Alert';

@Component({
  selector: 'app-view-port-aspect-ratio-list',
  templateUrl: './view-port-aspect-ratio-list.component.html',
  styleUrls: ['./view-port-aspect-ratio-list.component.scss'],
})
export class ViewPortAspectRatioListComponent implements OnInit {
  @Input() updateMapVisualizer: () => void;
  @Output() preferencesWindowWidth = new EventEmitter<{width:number, height:number}>(); 
  aspectRatios: Ratio[];

  constructor(private _userSettingsService: UserSettingsService) {}

  ngOnInit(): void 
  {
    this.aspectRatios = this._userSettingsService.data.aspectRatios;
  }

  async promptAddAspectRatio(): Promise<void> 
  {
    const aspectRatioString = await Alert.inputText(
      'Input an Aspect Ratio',
      'Example: "4x1" (W x H)'
    );
    if (!aspectRatioString) {
      return;
    }
    const aspectRatioSplit = aspectRatioString.split('x');
    const a = parseFloat(aspectRatioSplit[1]);
    const c = parseFloat(aspectRatioSplit[0]);
    if (!a || !c) {
      Alert.showError('Invalid Aspect Ratio');
      return;
    }
    this._userSettingsService.addAspectRatio(new Ratio(a, c));
    this.updateMapVisualizer();
  }

  removeAspectRatio(aspectRatio: Ratio): void 
  {
    this._userSettingsService.removeAspectRatio(aspectRatio);
    this.updateMapVisualizer();
  }

  updateMapAspectRatio(width : number, height : number)
  {
    let aspectRatio = {width: 0, height: 0};
    aspectRatio = {width: width, height: height};
    this.preferencesWindowWidth.emit(aspectRatio);
  }
}
