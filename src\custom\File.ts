// templates
import { ExportablePackage } from '../templates/ExportablePackage';
import { Versioning } from '../templates/Versioning';
import { AppService } from '../app/services/app.service';
import { Map, MapPoints, UserSettings } from '../models/mapsys1';

// dsadmin5 classes
import { Level, Area, DSAEvent, Item } from '../models/mapsys1';
import { MapService } from '../app/services/map.service';

//Decorator
import { DSAVersions } from '../models/dsa';
import { MapPointsService } from '../app/services/map-points.service';

// tslint:disable-next-line: no-namespace
export namespace File {

  export const MPS_SUFFIX_PATH = '.mps1.data';
  export const DSA_SUFFIX_PATH = '.dsadmin6.data';

  export enum MPS_PREFIX_PATH {
    'Map' = 'Map',
    'MapPoints' = 'MapPoints',
    'Area' = 'Area',
    'Level' = 'Level',
    'Item' = 'Item',
    'DSAEvent' = 'DSAEvent',
    'UserSettings' = 'UserSettings',
    'App' = 'App'
  }

  export enum DSA_PREFIX_PATH {
    'Area' = 'Area',
    'Class' = 'Class',
    'Dialogue' = 'Dialogue',
    'Emotion' = 'Emotion',
    'Event' = 'Event',
    'Item' = 'Item',
    'Level' = 'Level',
    'DSAEvent' = 'DSAEvent',
    'Marker' = 'Marker',
    'Mission' = 'Mission',
    'Objective' = 'Objective',
    'OptionBox' = 'OptionBox',
    'Option' = 'Option',
    'Speech' = 'Speech',
    'StoryBox' = 'StoryBox',
    'Tutorial' = 'Tutorial',
    'UserSettings' = 'UserSettings',
    'VideoMessage' = 'VideoMessage',
    'Video' = 'Video',
  }
  /**
   * Eligible File Formats names
   */
  export type FileFormat = 'dsa6' | 'mps1';

  export function getEnumStringValues<T>(myEnum: T): string[] {
    return Object.keys(myEnum) as any;
  }


  /**
   * DSADMIN v6 project file structure
   */
  export class MPS1 extends Versioning {

    readonly fileFormat: FileFormat = 'mps1';
    mapPackage: ExportablePackage<Map>;
    mapPointsPackage: ExportablePackage<MapPoints>;
    userSettings: UserSettings;

    constructor(
      myFile?: any,
      exportedDateTime?: string,
      appService?: AppService,
      mapService?: MapService,
      mapPointsService?: MapPointsService,
      userSettings?: UserSettings
    ) 
    {
      super(exportedDateTime, appService?.appVersion.tostring());

      this.mapPackage = mapService?.exportPackage(
        this._exportedDateTime,
        this._appVersion
      );

      this.mapPointsPackage = mapPointsService?.exportPackage(
        this._exportedDateTime,
        this._appVersion
      );


      this.userSettings = userSettings;
      if(myFile != null)
      {
        let mps = JSON.parse(myFile);
        this.mapPackage = mps.mapPackage;
        this.mapPointsPackage = mps.mapPointsPackage;
        this.userSettings = mps.userSettings;
      }
    }

    verifyIntegrity(): boolean 
    {
      return this.mapPackage ? (this.userSettings ? (this.mapPointsPackage ? true : false) : false) : false;
    }

    validationCheck() : Error
    {
      if(!this.verifyIntegrity())
        return Error('Invalid File');

      return null;
    }
  }


  export class DSA extends Versioning 
  {
    fileFormat: string;
    myAreaPackage: ExportablePackage<Area>;
    myEventPackage: ExportablePackage<DSAEvent>;
    myItemPackage: ExportablePackage<Item>;
    myLevelPackage: ExportablePackage<Level>;
    myMapPointsPackage: ExportablePackage<MapPoints>;

    fileVerified: boolean = true;

      constructor(private myFile?: any, exportedDateTime?: string, private appService?: AppService) 
      {
        super(exportedDateTime, appService?.appVersion.tostring());
        if(!myFile) return;

        this.fileFormat = myFile.fileFormat;

        if(!this.fileFormat.includes('dsa')) return;
        
      let version = DSAVersions.getVersion(this.fileFormat);

      //IMPORTANT: The order of this lines MUST follow the same sequence as the constructor of the dsa.ts class.
      this.myAreaPackage = eval('myFile.' + version.areaPackage);
      this.myLevelPackage = eval('myFile.' + version.levelPackage);
      this.myMapPointsPackage = eval('myFile.' + version.mapPointsPackage);
      this.myItemPackage = eval('myFile.' + version.itemPackage);
      this.myEventPackage = eval('myFile.' + version.eventPackage);

      this.fileVerified = (this.myAreaPackage != null) && (this.myLevelPackage != null) && (this.myMapPointsPackage != null) && (this.myItemPackage != null) && (this.myEventPackage != null);

    }

    validationCheck(): Error 
    {
      if (!this.fileFormat.includes('dsa')) 
      {
        return Error('Must be a DSA file.');
      }
      else if(!this.fileVerified)
      {
        return Error('Invalid DSA File');
      }
      else return;
    }
  }
}
