<div class="map-ratio-modal" *ngIf="isMapRatioModalOpen">
  <div class="map-ratio-modal-content">
    <div class="map-ratio-modal-header">
      <h2>Change Map Ratio</h2>  
    </div>
    <div class="map-ratio-modal-body">
      <div class="input-group">
        <label for="mapHeight">Height</label>
        <input class="b-input-left"  type="number" id="mapHeight" #mapHeight placeholder="Enter height">
      </div>
      <div class="input-group">
        <label for="screenCount">Number of Screens</label>
        <input class="b-input-right" type="number" id="screenCount" #screenCount placeholder="Enter number of screens">
      </div>
    </div>
    <div class="map-ratio-modal-footer">
        <button class="swal2-confirm swal2-styled" (click)="applyMapRatio(mapHeight.value, screenCount.value)">OK</button>
       <button class="swal2-cancel swal2-styled" (click)="closeMapRatioModal()">Cancel</button>      
    </div>
  </div>
</div>
