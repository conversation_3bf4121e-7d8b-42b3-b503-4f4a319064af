<div class="card">
    <div class="row middle">
        <div class="col-md-12" *ngIf="showImportDSAButton; else readingFromLocalFile">
            <div class="btn-group" style="width: 160px">
                <input class="input-file" id="dsa" type="file" #dsaFile (change)="
            promptDSAFile($event.target.files);
          " (click)="dsaFile.value = null" />
          <!-- <label class="btn btn-sm btn-fill middle" [ngClass]="lastLoadedTime ? 'btn-success' : 'btn-warning'" title="Imports a DSA JSON Project file" for="dsa">-->
                <label class="btn btn-sm btn-fill middle btn-success" title="Imports a DSA JSON Project file" for="dsa">
          <i class="icon pe-7s-download"></i>
          Import DSA
        </label>
            </div>
        </div>
        <ng-template #readingFromLocalFile>
            <div class="row middle category">
                <div class="col-md-12">Sync mode ON.</div>
            </div>
        </ng-template>
    </div>
</div>