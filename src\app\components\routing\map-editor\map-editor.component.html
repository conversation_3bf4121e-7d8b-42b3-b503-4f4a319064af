
<!-- THIS CODE FOLLOWS THE FLEXBOX. THE EXCEPTION IS THE CANVAS
https://css-tricks.com/snippets/css/a-guide-to-flexbox/-->
<!-- START: Whole page div -->
<div class="div-gerenal" (mousemove)="dragViewPort($event)" (mouseup)="releaseMouse()">

  <!-- START: Header div -->
  <div class="div-menu-top">
    <div class="title">
      {{ area?.name }}
    </div>
<!-- -------------------------------- -->
<ng-container *ngIf="isShowControlPoints">
    <div class="div-menu-btn1">
      <p class="p-hermite">Hermite Curve Properties</p>
      <div class="div-structure">

      <div class="thickness thickness-change-hermite">  
        <span class="span-thickeness">Curve thickeness</span>       
                <input type="number" min="1" max="10" #curveThickness class="input-thickeness"
                    (change)="controlLinesThickness(curveThickness.value)" [value]="this.currentHermiteCurveThickness">                     
          </div>                 

        <div style="display: flex; margin-left: -4px;">
          <input type="color" id="colorpicker" [value]="this.currentHermiteCurveColor" class="input-colorpicker"
              #HermiteCurveColor (change)="controlHermiteColor(HermiteCurveColor.value)" title="Give a color to the curve">
        </div>

           <div class="thickness thickness-hermite">
            <span class="span-tension">Curve tension</span>   
                    <input type="number" min="0" max="2" step="0.1" id="myRange"
                    [value]="this.currentHermiteCurveTension" class="input-tension"
                    #curveTension (change)="controlHermiteTension(curveTension.value)">                    
            </div>
      </div>
    </div>
</ng-container>
<!-- ----------------------------------------- -->
    <div id="menu" class="div-menu-btn">
      <!-- Grupo 1: Ações do Mapa -->
      <div class="div-action-map">
        <button title="Download map" class="btn btn-success btn-fill effect-btn btn-dowload-map" (click)="downloadMap()">
        <i class="pe-7s-next-2" style="margin-left: 3px;"></i>
        </button>
        <button title="Print map" class="btn btn-success btn-fill effect-btn btn-print-map" (click)="printMap()">
            <i class="pe-7s-print"></i>
        </button>
        <button title="Toggle level highlighting" class="btn btn-info btn-sm effect-btn btn-highlight-levels" [ngClass]="highlightLevelsInEditor ? 'btn-fill' : ''"
            (click)="toggleHighlightLevelsInEditor()">
            <i class="pe-7s-ribbon"></i>
        </button>
      </div>

      <!-- Grupo 2: Ferramentas de Edição -->
      <div style="display: flex; gap: 3px; align-items: center;">
        <button title="Draw on canvas" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowCanDraw ? '#C8C8C8' : ''}"
            (click)="this.canDraw()"> <i class="pe-7s-pen"></i>
        </button>
        <button title="Enable/Disable control points" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowControlPoints ? '#C8C8C8' : ''}"
            (click)="this.disableControlPoints()"> <i class="pe-7s-keypad"></i>
        </button>
        <button title="Show map in the background" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowBackground ? '#C8C8C8' : ''}"
            (click)="this.drawMapOnBackground()"> <i class="pe-7s-map-2"></i>
        </button>
        <button title="Show items from the levels" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowItems ? '#C8C8C8' : ''}"
            (click)="this.showItems()"> <i class="pe-7s-ticket"></i>
        </button>
      </div>

      <!-- Grupo 3: Visualização -->
      <div style="display: flex; gap: 3px; align-items: center; padding: 2px;">
        <button title="Show texts of places" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowText ? '#C8C8C8' : ''}"
          (click)="showText()"> <i class="pe-7s-comment"></i>
        </button>
        <button title="Show images of places" class="btn btn-success btn-fill blue-color btn-confi-menu-top" [ngStyle]="{'background-color': !isShowImage ? '#C8C8C8' : ''}"
          (click)="this.showImage()">
          <i class="pe-7s-photo" style="margin-left: 3px;"></i>
        </button>
      </div>
    </div>
  </div>
  <!-- END: Header div -->

 <!-- Modal Radio -->
  <ng-container *ngIf="isShowInputModal">
    <app-input-modal [map]="map" [viewPort]="viewPort" 
      (closeModal)="closeInputModal()" (applyRatio)="applyMapRatio($event)">
    </app-input-modal>
  </ng-container>

  <!-- START: Body div -->
  <div class="div-boby">

    <!-- START: left sidebar div -->
    <div class="div-sideBarModbile">
      <div class="div-sideBar">
        <h2 class="map-h2">Map</h2>
        <button type="number" class="btn btn-sm btn-fill btn-info effect-btn btn-radio" (click)="promptChangeMapRatio()">
          {{ map?.aspectRatio.a }} x {{ map?.aspectRatio.c }}
        </button>
        <div style="text-align: center;">
          <span>Altura equivale a {{(map?.aspectRatio.a / viewPort?.ratio?.a) | number:'1.2-2'}} 
            {{(map?.aspectRatio.a / viewPort?.ratio?.a | number:'1.0-0') == 1 ? 'ecrã' : 'ecrãs'}}
          </span>

        </div>
        <div class="div-rezide-map">
          <button class="btn btn-sm btn-fill btn-primary btn-border-style effect-btn" title="Change the width of the main canvas"
            (click)="resizeMap('width')">
            <i class="pe-7s-up-arrow"></i>
          </button>
          <button class="btn btn-sm btn-fill btn-primary btn-border-style effect-btn" title="Change the height of the main canvas"
            (click)="resizeMap('height')">
            <i class="pe-7s-bottom-arrow"></i>
          </button>
        </div>
        <div class="div-rezide-map">
          <button style="width: 48px;"
            class="btn btn-sm btn-info btn-border-style effect-btn" title="Zoom in"
            (click)="startZooming(-1)"
            >
            <i class="pe-7s-search" style="display: flex; justify-content: center;"></i>
          </button>
          <button style="width: 48px;"
            class="btn btn-sm btn-info btn-fill btn-border-style effect-btn" title="Zoom out"
            (click)="startZooming(1)"
            >
            <i class="pe-7s-search" style="display: flex; justify-content: center;"></i>
          </button>
        </div>
        <div class="div-rezide-map">
            <p>{{ zoomValue | number: '2.0-2'}} %
        </div>
        <div *ngIf="this.isShowBackground">Image: {{this.ImagePercentage}} %</div>
        <div *ngIf="this.isShowBackground">Screen: {{this.ScreenPercentage | number:'1.2-2'}} %</div>
      </div>
<!-- ------------------------------------------ -->
      <div class="div-draw" *ngIf="this.isShowCanDraw">
        <h2 class="draw-h2">Draw</h2>
        <button
            type="button" class="btn btn-danger effect-btn" style="padding:2px; width: 155px;"
            (click)="this.deleteDraw()" title="Delete ALL the draws on the canvas">Delete All
        </button>
        <button
            [ngStyle]="{'background-color': canDeleteDrawSection ? '#FF0059' : '', color:  canDeleteDrawSection ? '#FFFFFF' : '#FF0000'}"
            type="button" class="btn btn-danger effect-btn" style="padding:2px; width: 155px;" title="Delete a SELECTED segment on the canvas"
              (click)="this.deleteDrawSegment()">Delete Section
        </button>

        <div class="div-color-curve">
          <input type="color" id="colorpicker" [value]="this.currentSelectedColor"  style="margin-top: 6.5px;" 
                #colorPicker (change)="getColorPicker(colorPicker.value)" title="Give a color to the curve">

            <div class="div-currentCurveThickness">                
                <input type="number" min="1" max="30" [value]="currentCurveThickness" #curveThickness (change)="getCurveThickness(curveThickness.value)"
                 style="width: 55px; height: 26px; margin-top: 8px;" id="myRange" title="Change the curve thickeness">
           </div>

        </div>
      </div>
<!-- -------------------------------------------- -->
      <div class="div-viewport">
        <h2 class="display-h2">Display</h2>
        <button style="width: 140px" class="btn btn-sm btn-fill btn-info effect-btn" title="Change the preview window viewport dimension" (click)="changeViewPortRatio()">
          {{ viewPort?.ratio?.c }} x {{ viewPort?.ratio?.a }}
        </button>
        <div class="phone-frame">
            <div class="phone-top">
              <div class="phone-notch"></div>
            </div>
            <canvas title="Map Preview" id="view-port"
                    (mousedown)="getViewPortPivot($event)"
                    (wheel)="handleCanvasScroll($event)"></canvas>
            <div class="phone-bottom">
              <div class="home-button"></div>
            </div>
        </div>
    </div>
    </div>
    <!-- END: left sidebar div -->

    <app-modal *ngIf="this._modalService.canControlModal" (controlModal)="this.controlModalWindow($event)"></app-modal>

    <!-- START: Middle section div -->
    <div *ngIf="this.isShowBackground" class="div-canvas-dimension">
      <div class="dimension">Canvas Dimensions</div>
      <br>
      <div>
        <div class="div-Measure-Unit">
          <label for="files">Measure Unit</label>
          <select #unit (change)="this.exchangeInch2CM(unit.value)">
            <option value="Inches">Inches</option>
            <option value="Centimeters">Centimeters</option>
          </select>
        </div>
        <div class="div-diagonal-size">
          <label for="files">Diagonal Size:</label>
          <input #diagonal (keyup)="currentDimensions(diagonal.value)" placeholder="Enter diagonal" title="Enter diagonal" type="number">
        </div>
        <div class="div-diagonal-size">
          <label for="files">Desired PPI:</label>
          <input #ppi [value]="this.desiredPPI" (keyup)="desiredDimensions(ppi.value)" placeholder="Enter Desired PPI" title="Enter Desired PPI" type="number">
        </div>
      </div>
      <button class="btn-Listdevice effect-btn" (click)="this.calculateResolution()" style="margin-top: 5px;">Calculate Resolution</button>

    <!-- ----------------------------------------------------- -->
        <button class="btn-Listdevice effect-btn" (click)="this.showDevicesList()" class="btn-lis-device">List of Devices</button>
        <div style="overflow-y: scroll; margin-top: 5px;" *ngIf="this.canShowDevicesList">
          <ng-container *ngFor="let device of this.devices">
              <div (click)="this.showSelectedDevice(device)" class="card btn-device">
                  <p class="card-text">Device Name: {{ device.deviceName }}</p>
                  <p class="card-text">Device Type: {{ device.deviceType }}</p>
                  <p class="card-text">Device Size: {{ device.deviceSize }}</p>
                  <p class="card-text">Device PPI: {{ device.devicePPI }}</p>
                  <p class="card-text">Device Resolution: {{ device.deviceResolution }}</p>
              </div>
          </ng-container>
        </div>
        <div (click)="this.closeSelectedDevice()" *ngIf="this.canShowSelectedDevice" class="card btn-device">
          <p class="card-text">Device Name: {{ this.selectedDevice.deviceName }}</p>
          <p class="card-text">Device Type: {{ this.selectedDevice.deviceType }}</p>
          <p class="card-text">Device Size: {{ this.selectedDevice.deviceSize }}</p>
          <p class="card-text">Device PPI: {{ this.selectedDevice.devicePPI }}</p>
          <p class="card-text">Device Resolution: {{ this.selectedDevice.deviceResolution }}</p>
      </div>
      </div>
    <!-- ------------------------------------------------ -->

    <div id="editor-wrapper" [ngStyle]="this.map?.aspectRatio?.c > this.map?.aspectRatio?.a"
      class="middle-window">

      <!-- Container centralizado para o mapa -->
      <div class="map-container">

        <!-- Wrapper para canvas e div de interação -->
        <div class="canvas-wrapper" style="position: relative; display: inline-block;">

          <!-- Canvas único para todas as funcionalidades -->
          <canvas id="unified-canvas" class="unified-canvas-map"></canvas>

          <!-- Div de interação sobreposta para capturar eventos do mouse -->
          <div id="interaction-layer" class="div-interaction-layer"
               (mousemove)="handleMouseMove($event)"
               (mousedown)="handleMouseDown($event)"
               (mouseup)="handleMouseUp($event)"
               (mouseleave)="handleMouseLeave($event)">
          </div>

        </div>
      </div>
    </div>
    <!-- END: Middle section div -->

      <!-- START: right sidebar div -->
    <div class="div-sidebar-right">

      <ng-container *ngFor="let level of levelsFromArea; let i = index">
        <div [id]="level.id" class="tr-clickable level-row"
             [style.min-width]="levelHeights[level.id] * 30 + 'px'"
             (click)="createPoint(level.id)"
             (mouseenter)="highlightLevelOnMap(level.id, true)"
             (mouseleave)="highlightLevelOnMap(level.id, false)">
          <div class="circle-container">
            <div class="circle branch-circle middle alignLevels-circle"
              [ngStyle]="level.type | levelTypeCircle : level.id : map.points : newLevelIds"
              [class.highlighted-circle]="highlightedLevelId === level.id"
              (mouseenter)="highlightLevelOnMap(level.id, true)"
              (mouseleave)="highlightLevelOnMap(level.id, false)">
              {{ _areaService.getLevelIndex(level.id) }}
            </div>
          </div>
          <div class="level-name-text"
            [ngClass]="level.id | includedInMapStyle: map.points : newLevelIds">
            {{level.name}}
          </div>
        </div>
      </ng-container>
      <!-- ------------------------ -->
      <ng-container *ngFor="let level of textPoints; let i = index">
        <div [id]="level.id" class="tr-clickable color-line"
            class="div-menu-right" (mouseenter)="highlightTextPointOnMap(level.id, true)"
             (mouseleave)="highlightTextPointOnMap(level.id, false)"
             (click)="navigateToTextPoint(level.id)">
          <div class="circle branch-circle middle align-circle"
            [ngStyle]="level.classification | mapTextCircle"
            [class.highlighted-circle]="highlightedTextPointId === level.id">{{level.id}}
          </div>
          <div style="display: contents; width: -moz-fit-content; padding-right: 5px;"
            [ngClass]="level.id | includedInMapStyle: map.points : newLevelIds">
            {{level.name}}
          </div>
        </div>
      </ng-container>
    </div>
    <!-- END: right sidebar div -->

  </div>
  <!-- END: Body div -->

</div>
<!-- END: Whole page div -->
