import {Ratio, Layout, AnchorPoint, PixelDimension} from 'src/models/mapsys1';
import { Position } from './map-editor.component';

/**
 * Classe Canvas para gerenciamento de canvas HTML5
 * Responsável por cálculos de dimensões, posicionamento e renderização
 */
export class Canvas {
    /** Dimensão do canvas sem zona morta */
    public dimension: PixelDimension;
    /** Dimensão total do canvas incluindo zona morta */
    public canvasDimension: PixelDimension;
    /** Raio dos níveis em porcentagem */
    public levelRadius: number;
    /** Contexto 2D do canvas para renderização */
    public context : CanvasRenderingContext2D;

    /**
     * Construtor da classe Canvas
     * @param el Elemento HTML canvas
     * @param deadZoneSize Tamanho da zona morta em porcentagem
     * @param levelDiameter Diâmetro dos níveis
     * @param ratio Proporção de aspecto do canvas
     */
    constructor(
        public el: HTMLCanvasElement,
        public deadZoneSize: number,
        levelDiameter: number,
        public ratio: Ratio,
    )
    {
        this.levelRadius = levelDiameter * 0.5;
        this.context = el?.getContext('2d');
    }

    /**
     * Obtém o layout baseado na proporção de aspecto
     * @returns Layout vertical ou horizontal
     */
    get layout(): Layout
    {
        return this.ratio.a > this.ratio.c ? 'vertical' : 'horizontal';
    }

    /**
     * Calcula o tamanho da zona morta em pixels
     * @returns Tamanho da zona morta em pixels
     */
    get deadZoneInPixels(): number
    {
        return ((this.deadZoneSize * (this.layout === 'vertical' ? this.dimension.width : this.dimension.height)) * 0.01);
    }

    /**
     * Calcula o raio dos círculos em pixels
     * @returns Raio dos círculos em pixels
     */
    get circleRadiusInPixels(): number
    {
        return (this.layout === 'vertical' ? this.dimension.width : this.dimension.height) * this.levelRadius * 0.01;
    }

    /**
     * Calcula dimensão correspondente mantendo proporção
     * @param dimension Dimensão de referência
     * @param noDeadZone Se deve ignorar zona morta
     * @param layout Layout específico (opcional)
     * @returns Dimensão calculada
     */
    matchDimension(dimension: PixelDimension, noDeadZone?: boolean, layout?: Layout): PixelDimension
    {
        const ratio = noDeadZone ? this.dimension.toRatio() : this.ratio;
        return ratio.toDimension((layout ? layout === 'vertical' : this.layout === 'vertical') ?
        { width: dimension.width } : { height: dimension.height });
    }

    /**
     * Redimensiona o canvas com nova dimensão
     * @param dimension Nova dimensão
     * @param setDeadZone Se deve incluir zona morta
     * @param layout Layout específico (opcional)
     */
    redimension(dimension: PixelDimension, setDeadZone: boolean, layout?: Layout): void
    {
        this.dimension = this.ratio.toDimension((layout ? layout === this.layout : this.layout === 'horizontal') ?
        { height: dimension.height } : { width: dimension.width });

        if (setDeadZone)
        {
            this.canvasDimension = new PixelDimension(this.dimension.width + this.deadZoneInPixels * 2,
                 this.dimension.height + this.deadZoneInPixels * 2);
        }
        else
        {
            this.canvasDimension = this.dimension;
        }
        this.el.style.width = this.canvasDimension.width + 'px';
        this.el.style.height = this.canvasDimension.height + 'px';

        this.el.width = this.canvasDimension.width;
        this.el.height = this.canvasDimension.height;
    }
    
    /**
     * Converte posição percentual para pixels
     * @param position Posição em AnchorPoint (porcentagem)
     * @param withDeadZone Se deve incluir zona morta no cálculo
     * @returns Posição em pixels
     */
    positionInPixels(position: AnchorPoint, withDeadZone?: boolean): Position
    {
        if(!this.dimension.width) return;
        return {
            x: Math.floor(((withDeadZone ? this.canvasDimension.width : this.dimension.width) * position.xd) * 0.01),
            y: Math.floor(((withDeadZone ? this.canvasDimension.height : this.dimension.height) * position.yd) * 0.01),
        };
    }

    /**
     * Converte posição para pixels incluindo offset da zona morta
     * Baseado em código legado não abstraído
     * @param position Posição em AnchorPoint
     * @returns Posição com offset da zona morta
     */
    positionInPixelsDeadzone(position: AnchorPoint): AnchorPoint
    {
        let pos : Position = this.positionInPixels(position);
        return {
            xd: this.deadZoneInPixels + pos.x,
            yd: this.deadZoneInPixels + pos.y
        };
    }

    /**
     * Adiciona offset da zona morta a uma posição
     * Baseado em código legado não abstraído
     * @param position Posição em pixels
     * @returns Posição com offset da zona morta
     */
    positionInPixelsDeadzonePosition(position: Position): Position
    {
        return {
            x: this.deadZoneInPixels + position.x,
            y: this.deadZoneInPixels + position.y
        };
    }

    /**
     * Converte posição percentual para pixels usando dimensão total do canvas
     * Para mais informações, consulte a documentação pelo nome do método
     * @param position Posição em AnchorPoint
     * @returns Posição em pixels como AnchorPoint
     */
    positionInPixelsForAnchorpoint(position: AnchorPoint): AnchorPoint
    {
        return {
            xd: (this.canvasDimension.width * position?.xd) / 100,
            yd: (this.canvasDimension.height * position?.yd) / 100,
        };
    }

    /**
     * Mesmo que positionInPixelsForAnchorpoint mas retorna Position
     * @param position Posição em AnchorPoint
     * @returns Posição em pixels como Position
     */
    positionInPixelsForPosition(position: AnchorPoint): Position
    {
        return {
            x: (this.canvasDimension.width * position?.xd) / 100,
            y: (this.canvasDimension.height * position?.yd) / 100,
        };
    }

    /**
     * Converte pixels para porcentagem (inverso de positionInPixelsForAnchorpoint)
     * @param position Posição em pixels
     * @param withDeadZone Se deve considerar zona morta
     * @returns Posição em porcentagem
     */
    fromPixelsToPercentage(position: AnchorPoint, withDeadZone?: boolean): AnchorPoint
    {
        return {
            xd: Math.floor((withDeadZone ? (100*position.xd)/this.canvasDimension.width : (100*position.xd)/this.dimension.width)),
            yd: Math.floor((withDeadZone ? (100*position.yd)/this.canvasDimension.height : (100*position.yd)/this.dimension.height)),
        };
    }
  
}
