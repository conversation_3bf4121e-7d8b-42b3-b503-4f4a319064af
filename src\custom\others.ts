import { File } from './File';

/**
 * Conta a quantidade de palavras em uma string
 * @param str String para contar palavras
 * @returns Número de palavras na string
 */
export function amountOfWords(str: string): number {
  return str?.trim().split(/\W+/).length || 0;
}

/**
 * Obtém valores de filtro do localStorage
 * @param typeName Nome do tipo para buscar filtros
 * @returns Objeto com valores de filtro ou objeto vazio
 */
function getFilterValues(typeName: string): void
{
  return (
    JSON.parse(
      localStorage.getItem(typeName + '-filters' + File.MPS_SUFFIX_PATH)
    ) || {}
  );
}
/**
 * Define um valor de filtro no localStorage
 * @param value Valor a ser definido
 * @param typeName Nome do tipo
 * @param filterName Nome do filtro
 */
export function setFilterValue(
  value: any,
  typeName: string,
  filterName: string
): void {
  const filterValues = getFilterValues(typeName);
  filterValues[filterName] = value;
  localStorage.setItem(
    typeName + '-filters' + File.MPS_SUFFIX_PATH,
    JSON.stringify(filterValues)
  );
}
/**
 * Obtém um valor de filtro específico do localStorage
 * @param typeName Nome do tipo
 * @param filterName Nome do filtro
 * @returns Valor do filtro ou undefined
 */
export function getFilterValue(typeName: string, filterName: string): any {
  const filterValues = getFilterValues(typeName);
  return filterValues[filterName];
}

/**
 * Obtém a data da última modificação de um tipo
 * @param typeName Nome do tipo
 * @returns Data da última modificação como string
 */
export function getLastModified(typeName: string): string {
  return localStorage.getItem(typeName + 'last' + File.MPS_SUFFIX_PATH);
}

/**
 * Define a data da última modificação de um tipo
 * @param data Data da modificação
 * @param typeName Nome do tipo
 */
export function setLastModified(data: string, typeName: string): void {
  localStorage.setItem(typeName + 'last' + File.MPS_SUFFIX_PATH, data);
}

/**
 * Armazena dados no localStorage
 * @param data Dados para armazenar
 * @param typeName Nome do tipo
 * @param suffix Sufixo para a chave
 */
export function setData(data: any, typeName: string, suffix: string): void {
  localStorage.setItem(typeName + suffix, JSON.stringify(data));
}

/**
 * Obtém dados do localStorage
 * @param typeName Nome do tipo
 * @param suffix Sufixo da chave
 * @returns Dados deserializados do tipo T
 */
export function getData<T>(typeName: string, suffix: string): T {
  return JSON.parse(localStorage.getItem(typeName + suffix)) as T;
}

/**
 * Compara se duas strings são iguais ignorando acentos e case
 * @param str Primeira string
 * @param str2 Segunda string
 * @returns True se as strings são equivalentes
 */
export function stringIsEqual(str: string, str2: string): boolean {
  return comparableString(str) === comparableString(str2);
}

/**
 * Converte string para formato comparável (sem acentos, maiúscula)
 * @param str String para converter
 * @returns String normalizada para comparação
 */
export function comparableString(str: string): string {
  return removeAccents(str)?.toLocaleUpperCase();
}

/**
 * Remove acentos de uma string
 * @param str String com acentos
 * @returns String sem acentos
 */
export function removeAccents(str: string): string {
  return str?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

export interface Index<T> {
  [id: string]: T;
}

export interface ReviewRequest {
  typeName: string;
  fullReview?: boolean;
  parameters: {
    objectId?: string;
    levelId?: string;
  };
}

export interface ReviewInfo {
  parametersWithErrors?: string[];
  dialogueId?: string;
  levelId?: string;
  assignedAt?: string[];
  levelsAssignedToFromSameArea?: number;
  // completedBy?: string[] = [];
  objectivesCompleted?: boolean;
  receivedAt?: string[];
  givenAt?: string[];
  tradedAt?: string[];
  completesAt?: string[];
  failsAt?: string[];
  characterIds?: string[];

  match3BlockedBy?: string[];
  unlockedBy?: string[];
  unlockedMissionDialogueBy?: string[];
  linkListInOrder?: boolean;
  missionDialogueNotYetUnlocked?: boolean;

  parent_levelIds?: string[];
  parent_levelIds_otherArea?: string[];
  parent_levelIds_sameArea?: string[];

  child_levelIds?: string[];
  child_levelIds_otherArea?: string[];
  child_levelIds_sameArea?: string[];

  notTransition_linksToOtherArea?: boolean;
  isTransition_noLinksToOtherArea?: boolean;

  noBattleCharacters?: boolean;
  index?: number;
  amountOfWords?: number;
}
/* export class CharacterReviewInfo extends ReviewInfo {

} */

export function getInputList(
  list: any[],
  valueParameter: string,
  byParameters: string[],
  orderByParameter: boolean
): { [inputValue: string]: string } {
  const res = {};
  list = orderByParameter ? sortData(list, byParameters[0]) : list;
  list.forEach((element) => {
    res[element[valueParameter]] =
      (element[byParameters[0]] || '') +
      (byParameters[1] ? ': ' + element[byParameters[1]] : '');
  });
  return res;
}
export function getIconInputList(
  list: any[],
  orderByParameter: boolean
): { [inputValue: string]: string } {
  const res = {};
  list = orderByParameter ? list.sort((a, b) => (a > b ? 1 : -1)) : list;
  list.forEach((element) => {
    res[element] = element.split('7s-')[1];
  });
  return res;
}

export function readMultiple(objs): string[] {
  const str: string[] = [];
  objs.forEach((obj) => {
    str.push(this.read(obj));
  });
  return str;
}
export function read(obj: any): string {
  // if (!obj) { return; }
  return obj.name ? obj.id + ': ' + obj.name : obj.id;
}

export function comparableNumber(str: string): number {
  if (str === undefined || str?.replace(/[^0-9]/g, '') === '') {
    return undefined;
  }
  return parseFloat(str);
}

export function extractString(str: string): string {
  return str?.replace(/[0-9]/g, '');
}
export function extractInt(str: string): number {
  return parseInt(str?.replace(/[^0-9]/g, ''), 10);
}

export function sortData<T extends any>(data: T[], parameter: string): T[] {
  /* if (parameter === 'locationId') {
    return data.sort((a, b) => { return extractInt(a.id.split('.')[0]) > extractInt(b.id.split('.')[0]) ? 1 : -1; });
  } */
  if (parameter === 'index') {
    return data;
    // return data.sort((a, b) => { return data.indexOf(a) > data.indexOf(b) ? 1 : -1; });
  } else if (parameter === 'id') {
    return data.sort((a, b) =>
      extractInt(a[parameter]) > extractInt(b[parameter]) ? 1 : -1
    );
  } else if (parameter === 'hierarchyCode') {
    return data.sort((a, b) =>
      compareCodes(a[parameter], b[parameter]) > 0 ? 1 : -1
    );
  } else {
    return data.sort((a, b) =>
      comparable(a[parameter]) > comparable(b[parameter]) ||
      a[parameter] === undefined
        ? 1
        : -1
    );
  }
}

export function compareCodes(a: string, b: string): number {
  if (a === undefined) {
    return -1;
  }
  if (b === undefined) {
    return 1;
  }

  const regex = new RegExp('([0-9]+)|([a-zA-Z]+)', 'g');

  const aCode = a.match(regex);
  const bCode = b.match(regex);
  let weight = 0;

  aCode.forEach((aCodePart, i) => {
    if (weight !== 0) {
      return;
    }
    const bCodePart = bCode[i];
    if (bCodePart === undefined) {
      weight = 1;
      return;
    }
    const aString = extractString(aCodePart);
    if (aString) {
      const bString = extractString(bCodePart);
      weight = aString > bString ? 1 : aString === bString ? 0 : -1;
    } else {
      const aNumber = extractInt(aCodePart);
      const bNumber = extractInt(bCodePart);
      weight = aNumber - bNumber;
    }
  });

  return weight;
}

export function comparable(value: any): any {
  if (typeof value === 'string') {
    return value ? comparableString(value) : undefined;
  }
  return value;
}

export function highlightElement(
  elementId: string,
  miliseconds: number,
  scroll: boolean,
  color?: string,
  dontReset?: boolean,
  arg?: boolean | ScrollIntoViewOptions
): void {
  setTimeout(() => {
    const element: HTMLElement = document.getElementById(elementId);
    if (element != null) {
      // Preserva a cor de fundo original do elemento
      const lastBackgroundColor = element.style.backgroundColor.toString();
      // Destaca o elemento
      element.style.backgroundColor = color || '#c4baff';
      if (scroll) {
        element?.scrollIntoView(
          arg || {
            block: 'nearest' /* , inline: 'center' */,
          }
        );
      }
      if (!dontReset) {
        setTimeout(() => {
          // Reseta o destaque do elemento
          element.style.backgroundColor = lastBackgroundColor;
        }, 300);
      }
    }
  }, miliseconds);
}
