import { Injectable } from '@angular/core';
import { Version } from '../../templates/Versioning';
import { Subject } from 'rxjs';
import { getData, setData } from '../../custom/others';
import { File } from '../../custom/File';

/**
 * Serviço principal da aplicação
 * Gerencia informações globais como versão, build e tempos de carregamento
 */
@Injectable({
  providedIn: 'root',
})
export class AppService
{
  /** Nome do tipo para armazenamento */
  typeName = 'App';
  /** Versão atual da aplicação */
  appVersion: Version = new Version(1, 6, 0);
  /** Build atual da aplicação */
  build = '2025_0613';
  /** Dados da aplicação */
  data:
  {
    /** Último tempo de carregamento DSA */
    DSA_lastLoadedTime?: Date;
    /** Último tempo de carregamento MPS */
    MPS_lastLoadedTime?: Date;
  };
  /** Subject para notificar mudanças no tempo de carregamento DSA */
  lastDSALoadedTimeSubject: Subject<Date> = new Subject<Date>();
  /** Subject para notificar mudanças no tempo de carregamento MPS */
  lastMPSLoadedTimeSubject: Subject<Date> = new Subject<Date>();

  /**
   * Construtor do serviço da aplicação
   * Carrega dados salvos automaticamente
   */
  constructor()
  {
    this.loadData();
  }

  /**
   * Define o último tempo de carregamento DSA
   * @param value Data do carregamento
   */
  setDSALastLoadedTime(value: Date): void
  {
    this.data.DSA_lastLoadedTime = value;
    this.lastDSALoadedTimeSubject.next(this.data.DSA_lastLoadedTime);
    this.saveData();
  }

  /**
   * Define o último tempo de carregamento MPS
   * @param value Data do carregamento
   */
  setMPSLastLoadedTime(value: Date): void
  {
    this.data.MPS_lastLoadedTime = value;
    this.lastMPSLoadedTimeSubject.next(this.data.MPS_lastLoadedTime);
    this.saveData();
  }

  /**
   * Carrega dados do localStorage
   * Inicializa dados se não existirem
   */
  private loadData(): void
  {
    this.data = getData(this.typeName, File.MPS_SUFFIX_PATH) || {};
    this.saveData();
  }

  /**
   * Salva dados no localStorage
   */
  private saveData(): void
  {
    setData(this.data, this.typeName, File.MPS_SUFFIX_PATH);
  }
}
