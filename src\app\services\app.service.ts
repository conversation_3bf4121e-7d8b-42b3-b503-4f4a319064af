import { Injectable } from '@angular/core';
import { Version } from '../../templates/Versioning';
import { Subject } from 'rxjs';
import { getData, setData } from '../../custom/others';
import { File } from '../../custom/File';

@Injectable({
  providedIn: 'root',
})
export class AppService 
{
  typeName = 'App';
  appVersion: Version = new Version(1, 6, 0);
  build = '2025_0613';
  data: 
  {
    DSA_lastLoadedTime?: Date;
    MPS_lastLoadedTime?: Date;
  };
  lastDSALoadedTimeSubject: Subject<Date> = new Subject<Date>();
  lastMPSLoadedTimeSubject: Subject<Date> = new Subject<Date>();

  constructor() 
  {
    this.loadData();
  }

  setDSALastLoadedTime(value: Date): void 
  {
    this.data.DSA_lastLoadedTime = value;
    this.lastDSALoadedTimeSubject.next(this.data.DSA_lastLoadedTime);
    this.saveData();
  }

  setMPSLastLoadedTime(value: Date): void 
  {
    this.data.MPS_lastLoadedTime = value;
    this.lastMPSLoadedTimeSubject.next(this.data.MPS_lastLoadedTime);
    this.saveData();
  }

  private loadData(): void 
  {
    this.data = getData(this.typeName, File.MPS_SUFFIX_PATH) || {};
    this.saveData();
  }

  private saveData(): void 
  {
    setData(this.data, this.typeName, File.MPS_SUFFIX_PATH);
  }
}
