import { ChangeLog } from './../../../custom/ChangeLog';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapService } from '../../../app/services/map.service';
import { Subscription } from 'rxjs';
import { AppService } from '../../../app/services/app.service';

declare const $: any;
declare interface RouteInfo {
  path: string;
  title: string;
  icon: string;
}
/**
 * Componente da barra lateral que exibe links e notificações de erro
 */
@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  public enabledMapEditor: boolean;
  public mapEditorSubscription: Subscription;
  appSubscription: Subscription;
  lastDSALoadedTime: Date;
  /**
   * Armazena a versão da aplicação
   */
  public appVersion: string;
  /**
   * Armazena o build da aplicação
   */
  public build: string;

  /**
   * Armazena rotas para exibir no topo do menu
   */
  public menuTabs: RouteInfo[] = [
    { path: '/manager', title: 'Maps Manager', icon: 'pe-7s-map' },
  ];
  public preferencesTab: RouteInfo = {
    path: '/preferences',
    title: 'Settings',
    icon: 'pe-7s-config',
  };

  /**
   * Armazena rotas para exibir na parte inferior do menu
   */
  public mapEditorTab: RouteInfo = {
    path: '/map-editor',
    title: 'Last Modified',
    icon: 'pe-7s-map-2',
  };

  public activePath = '/dashboard';

  /**
   * Inscreve-se nos subjects necessários para exibir notificações
   */
  constructor(
    private _mapService: MapService,
    private _appService: AppService
  ) {
    this.appVersion = this._appService.appVersion.tostring();
    this.build = this._appService.build;
  }

  /**
   * Inicializa o componente e configura as inscrições
   */
  ngOnInit(): void {
    this.build = this._appService.build;
    this.enabledMapEditor = this._mapService.findById(
      this._mapService.lastModifiedId
    )
      ? true
      : false;

    this.mapEditorSubscription = this._mapService.lastIdModification.subscribe(
      (lastId) => {
        this.enabledMapEditor = this._mapService.findById(lastId)
          ? true
          : false;
      }
    );
    this.lastDSALoadedTime = this._appService.data.DSA_lastLoadedTime;
    this.appSubscription = this._appService.lastDSALoadedTimeSubject.subscribe(
      (value) => {
        this.lastDSALoadedTime = value;
      }
    );
  }

  /**
   * Limpa as inscrições ao destruir o componente
   */
  ngOnDestroy(): void {
    this.mapEditorSubscription.unsubscribe();
    this.appSubscription.unsubscribe();
  }

  /**
   * Exibe o log de alterações
   */
  PromptChangeLog(): void {
    let steps : string[] = [];
    ChangeLog.forEach((change, index) => {
      steps.push(index.toString());
    });
    // Alert.showChainingAlert(ChangeLog,steps); // Comentado temporariamente
  }

}
