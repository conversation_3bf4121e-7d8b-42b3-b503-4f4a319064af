import { ChangeLog } from './../../../custom/ChangeLog';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapService } from '../../../app/services/map.service';
import { Subscription } from 'rxjs';
import { AppService } from '../../../app/services/app.service';

declare const $: any;
declare interface RouteInfo {
  path: string;
  title: string;
  icon: string;
}
/**
 * Sidebar component that shows links and error notifications
 */
@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  public enabledMapEditor: boolean;
  public mapEditorSubscription: Subscription;
  appSubscription: Subscription;
  lastDSALoadedTime: Date;
  /**
   * Stores the app version
   */
  public appVersion: string;
  /**
   * Stores the app build
   */
  public build: string;

  /**
   * Stores routes to show on the top of the menu
   */
  public menuTabs: RouteInfo[] = [
    { path: '/manager', title: 'Maps Manager', icon: 'pe-7s-map' },
  ];
  public preferencesTab: RouteInfo = {
    path: '/preferences',
    title: 'Preferences',
    icon: 'pe-7s-plugin',
  };

  /**
   * Stores routes to show on the bottom of the menu
   */
  public mapEditorTab: RouteInfo = {
    path: '/map-editor',
    title: 'Last Modified',
    icon: 'pe-7s-map-2',
  };

  public activePath = '/dashboard';

  /**
   * Subscribes to necessary subects to display notifications
   */
  constructor(
    private _mapService: MapService,
    private _appService: AppService
  ) {
    this.appVersion = this._appService.appVersion.tostring();
    this.build = this._appService.build;
  }

  ngOnInit(): void {
    this.build = this._appService.build;
    this.enabledMapEditor = this._mapService.findById(
      this._mapService.lastModifiedId
    )
      ? true
      : false;

    this.mapEditorSubscription = this._mapService.lastIdModification.subscribe(
      (lastId) => {
        this.enabledMapEditor = this._mapService.findById(lastId)
          ? true
          : false;
      }
    );
    this.lastDSALoadedTime = this._appService.data.DSA_lastLoadedTime;
    this.appSubscription = this._appService.lastDSALoadedTimeSubject.subscribe(
      (value) => {
        this.lastDSALoadedTime = value;
      }
    );
  }

  ngOnDestroy(): void {
    this.mapEditorSubscription.unsubscribe();
    this.appSubscription.unsubscribe();
  }

  /**
   * Prompts the change log
   */
  PromptChangeLog(): void {
    let steps : string[] = [];
    ChangeLog.forEach((change, index) => {
      steps.push(index.toString());
    });
    //Alert.showChainingAlert(ChangeLog,steps);
  }

}
