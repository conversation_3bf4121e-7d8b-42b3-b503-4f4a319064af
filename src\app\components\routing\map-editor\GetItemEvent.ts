import { EventService } from '../../../../app/services/event.service';
import { ItemService } from '../../../../app/services/item.service';
import { MapService } from '../../../../app/services/map.service';
import { Map } from '../../../../models/mapsys1';
import { ItemEvent } from '../../../../models/mapsys1'
import { ItemType } from '../../../../models/mapsys1'

export class GetItemEvent 
{
    private _eventService : EventService;
    private _itemService : ItemService;
    itemEvent : ItemEvent[] = [];
    map : Map;

    constructor(private _mapService : MapService) 
    {
        
        this._eventService = new EventService();
        this._itemService = new ItemService();
    }
    
    public initializeItemEvent() : ItemEvent[]
    {
        this.map = this._mapService.findById(this._mapService.lastModifiedId);
        this.itemEvent = [];
        let points = Object.keys(this.map.points);
       
        this.createEventItem(points);
        this.addItemName();
        
        return this.itemEvent;
    }

    private createEventItem(points)
    {
        //Create an ItemEvent from an area using eventPkg.
        for(let j = 0; j < this._eventService.data.length; j++)
        {
            if(this._eventService.data[j].itemId != undefined &&
                this._eventService.data[j].id.split('.')[0] == this.map.areaId)
            {
                let newItemEvent = new ItemEvent();

                let aux = this._eventService.data[j].id.split('.');
                newItemEvent.levelID = aux[0]+'.'+aux[1];
                newItemEvent.itemID = this._eventService.data[j].itemId;
                newItemEvent.itemName = '';//We do not have access to this. It is filled below.
                newItemEvent.itemType = ItemType[this._eventService.data[j].type];

                for(let i = 0; i < points.length; i++)
                {
                    if(points[i] == newItemEvent.levelID)
                    {
                        newItemEvent.levelXPosition = this.map.points[points[i]].position.xd;
                        newItemEvent.levelYPosition = this.map.points[points[i]].position.yd;
                        break;
                    }
                }
                this.itemEvent.push(newItemEvent);
            }
        }
    }

    private addItemName()
    {
        //Add itemName from the itemPkg list
        for(let i = 0; i < this._itemService.data.length; i++)
        {
            for(let j = 0; j < this.itemEvent.length; j++)
            {
                if(this.itemEvent[j].itemID == this._itemService.data[i].id)
                {
                    this.itemEvent[j].itemName = this._itemService.data[i].name;
                }
            }
        }
    }
}