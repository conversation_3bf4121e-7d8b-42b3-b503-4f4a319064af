import { Router } from '@angular/router';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { UserSettingsService } from '../../../../app/services/user-settings.service';
import { File } from '../../../../custom/File';
import { AreaService } from '../../../../app/services/area.service';
import { LevelService } from '../../../../app/services/level.service';
import { MapService } from '../../../../app/services/map.service';
import { Alert } from '../../../../custom/Alert';
import { Subscription } from 'rxjs';
import { AppService } from '../../../../app/services/app.service';
import { ASPECT_RATIO_PRESETS, PixelDimension } from '../../../../models/mapsys1';
import { DataService } from './../../../../app/services/data.service';
import { MapPointsService } from '../../../../app/services/map-points.service';

/**
 * Componente de preferências do sistema
 * Gerencia configurações do usuário, armazenamento local e visualização de mapas
 */
@Component({
  selector: 'app-preferences',
  templateUrl: './preferences.component.html',
  styleUrls: ['./preferences.component.scss'],
})
export class PreferencesComponent implements OnInit, OnDestroy
{
  /** Constante para sufixo de caminho DSA */
  readonly DSA_SUFFIX_PATH = File.DSA_SUFFIX_PATH;
  /** Constante para sufixo de caminho MPS */
  readonly MPS_SUFFIX_PATH = File.MPS_SUFFIX_PATH;

  /** Diâmetro dos níveis no mapa */
  levelDiameter: number;
  /** Tamanho da zona morta */
  deadZoneSize: number;
  /** Contexto do canvas para renderização */
  public context : CanvasRenderingContext2D;
  /** Timeout para operações assíncronas */
  firstTimeout

  /**
   * Obtém os caminhos de dados MPS do localStorage
   * @returns Array de caminhos MPS
   */
  get mpsDataPaths(): string[]
  {
    const paths: string[] = [];
    File.getEnumStringValues(File.MPS_PREFIX_PATH).forEach((prefix) =>
    {
      paths.push(prefix + File.MPS_SUFFIX_PATH);
    });
    return paths;
  }

  /**
   * Obtém os caminhos de dados DSA do localStorage
   * @returns Array de caminhos DSA
   */
  get dsaDataPaths(): string[]
  {
    const paths: string[] = [];
    File.getEnumStringValues(File.DSA_PREFIX_PATH).forEach((prefix) =>
    {
      paths.push(prefix + File.DSA_SUFFIX_PATH);
    });
    return paths;
  }

  /** Informações sobre o tamanho do localStorage em KB */
  localStorageSizeInKB:
  {
    /** Total geral */
    total: number;
    /** Total MPS */
    mpsTotal: number;
    /** Dados MPS por prefixo */
    mps: { [prefix: string]: number };
    /** Total DSA */
    dsaTotal: number;
    /** Dados DSA por prefixo */
    dsa: { [prefix: string]: number };
    /** Outros dados */
    others: number;
  };

  /** Inscrição para carregamento de arquivos */
  fileLoadSubscription: Subscription;
  /** Flag para detectar dados DSA do localStorage */
  detectDSADataFromLocalStorage: boolean;
  /** Inscrição para detecção de dados DSA */
  DSADataFromLocalStorageDetectionSubscription: Subscription;

  /** Canvas para visualização do mapa */
  mapVisualizer: HTMLCanvasElement;

  /** Função para atualizar o visualizador de mapa */
  updateMapVisualizerFunc;

  /**
   * Construtor do componente de preferências
   * Injeta todos os serviços necessários e configura funções
   */
  constructor(
    private _userSettingsService: UserSettingsService,
    private _appService: AppService,
    private _router: Router,
    private _dataService: DataService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _mapService: MapService,
    private _mapPointsService: MapPointsService,
    )
  {
    this.updateMapVisualizerFunc = this.updateMapVisualizer.bind(this);
  }

  ngOnInit(): void 
  {
    this.calculateLocalStorage();
    this.fileLoadSubscription = this._appService.lastDSALoadedTimeSubject.subscribe(() => 
    {
        this.calculateLocalStorage();
    });

    this.fileLoadSubscription.add(this._appService.lastMPSLoadedTimeSubject.subscribe(() => 
      {
        this.calculateLocalStorage();
      })
    );

    this.detectDSADataFromLocalStorage = this._userSettingsService.data.detectDSADataFromLocalStorage;
    this.DSADataFromLocalStorageDetectionSubscription = this._userSettingsService.lastPreferencesChange.subscribe(() => 
    {
      this.toggleDSADetectionButton();
    });

    this.deadZoneSize = this._userSettingsService.data.deadZoneSize;
    this.levelDiameter = this._userSettingsService.data.levelDiameter;

    this.mapVisualizer = document.getElementById('map-visualizer') as HTMLCanvasElement;
    this.context = this.mapVisualizer.getContext('2d');

    this.updateMapVisualizer(this.calculateCanvasDimension(), this.calculateDeadzoneInPixels());

  }

  ngOnDestroy(): void 
  {
    this.fileLoadSubscription.unsubscribe();
    this.DSADataFromLocalStorageDetectionSubscription.unsubscribe();
    clearTimeout(this.firstTimeout);
  }

  private calculateLocalStorage(): void 
  {
    this.localStorageSizeInKB = {total: 0, mpsTotal: 0, mps: {}, dsaTotal: 0, dsa: {}, others: 0};
    this.calculateMPSLocalStorageInKB();
    this.calculateDSALocalStorageInKB();
    this.calculateTotalLocalStorageInKB();
    this.localStorageSizeInKB.others = this.localStorageSizeInKB.total - this.localStorageSizeInKB.mpsTotal;
  }
  private calculateMPSLocalStorageInKB(): void 
  {
    this.mpsDataPaths.forEach((path) => 
    {
      if (!localStorage.hasOwnProperty(path)) return;
      
      this.localStorageSizeInKB.mps[path] = (localStorage[path].length + path.length) * 2;
      this.localStorageSizeInKB.mpsTotal += this.localStorageSizeInKB.mps[path];
    });
  }

  private calculateDSALocalStorageInKB(): void 
  {
    this.dsaDataPaths.forEach((path) => 
    {
      if (!localStorage.hasOwnProperty(path)) return;
      
      this.localStorageSizeInKB.dsa[path] = (localStorage[path].length + path.length) * 2;
      this.localStorageSizeInKB.dsaTotal += this.localStorageSizeInKB.dsa[path];
    });
  }

  private calculateTotalLocalStorageInKB(): void 
  {
    for (const x in localStorage) 
    {
      if (!localStorage.hasOwnProperty(x)) continue;
      
      const len = (localStorage[x].length + x.length) * 2;
      this.localStorageSizeInKB.total += len;
    }
  }

  async promptToggleDSADetection(value: boolean): Promise<void> 
  {
    if (!value ||
       (await Alert.confirm(
        'Confirm',
        'Changing this setting will clear current imported DSA project file from the app.',
        'Yes, clear app data and load local storage data'
      ))) 
    {
      this.toggleDSADetection(value);
    } 
    else 
    {
     this.firstTimeout = setTimeout(() => 
      {
        this.toggleDSADetectionButton();
      }, 1000);
    }
  }

  private toggleDSADetectionButton(): void 
  {
    const toggleTrueButton = document.getElementById('toggle-true');
    const toggleFalseButton = document.getElementById('toggle-false');
    const toggle = document.getElementById(
      this._userSettingsService.data.detectDSADataFromLocalStorage ? 'toggle-true' : 'toggle-false');

    if (!toggle) return;
    
    toggle.focus();
    toggleTrueButton.className = 'btn btn-lg btn-primary btn-simple' +
     (this._userSettingsService.data.detectDSADataFromLocalStorage ? ' focus active' : '');

    toggleFalseButton.className = 'btn btn-lg btn-primary btn-simple' +
      (!this._userSettingsService.data.detectDSADataFromLocalStorage ? ' focus active' : '');
  }

  private toggleDSADetection(value: boolean): void 
  {
    this._userSettingsService.detectDSADataFromLocalStorage(value);
    this.calculateLocalStorage();
  }

  setLevelDiameter(value: number): void 
  {
    this._userSettingsService.setLevelDiameter(value);
    this.levelDiameter = value;
    this.updateMapVisualizer(this.calculateCanvasDimension(), this.calculateDeadzoneInPixels());

  }

  changeDeadZoneSize(value: number): void 
  {
    this._userSettingsService.setDeadZoneSize(value);
    this.deadZoneSize = value;
    this.updateMapVisualizer(this.calculateCanvasDimension(), this.calculateDeadzoneInPixels());
  }

  calculateDeadzoneInPixels() : number
  {
   
    const mainAspectRatio = this._userSettingsService.data.aspectRatios[0] || ASPECT_RATIO_PRESETS[0];
    const dimension = mainAspectRatio.toDimension(mainAspectRatio.c > mainAspectRatio.a ? { width: 300 } : { height: 300 });
    const deadZoneInPixels = mainAspectRatio.a > mainAspectRatio.c ? 
    (this.deadZoneSize * dimension.width) / 100 : (this.deadZoneSize * dimension.height) / 100;

    return deadZoneInPixels;
  }

  calculateCanvasDimension() : PixelDimension
  {
    const mainAspectRatio = this._userSettingsService.data.aspectRatios[0] || ASPECT_RATIO_PRESETS[0];
    const dimension = mainAspectRatio.toDimension(mainAspectRatio.c > mainAspectRatio.a ? { width: 300 } : { height: 300 });
    const canvasDimension = new PixelDimension(
      dimension.width + this.calculateDeadzoneInPixels(),
      dimension.height + this.calculateDeadzoneInPixels()
    );

    return canvasDimension;
  }


  updateMapVisualizer(canvasDimension, deadZoneInPixels): void 
  {

    this.mapVisualizer.width = canvasDimension.width;
    this.mapVisualizer.height = canvasDimension.height;

    this.context.clearRect(0, 0,this.mapVisualizer.width, this.mapVisualizer.height);
    this.context.strokeStyle = 'white';
    this.context.beginPath();

    this.context.moveTo(0, 0);
    this.context.lineTo(deadZoneInPixels, deadZoneInPixels);
    this.context.lineTo(this.mapVisualizer.width - deadZoneInPixels, deadZoneInPixels);
    this.context.moveTo(this.mapVisualizer.width, 0);
    this.context.lineTo(this.mapVisualizer.width - deadZoneInPixels, deadZoneInPixels);
    this.context.lineTo(this.mapVisualizer.width - deadZoneInPixels,this.mapVisualizer.height - deadZoneInPixels);
    this.context.moveTo(this.mapVisualizer.width, this.mapVisualizer.height);
    this.context.lineTo(this.mapVisualizer.width - deadZoneInPixels, this.mapVisualizer.height - deadZoneInPixels);
    this.context.lineTo(deadZoneInPixels, this.mapVisualizer.height - deadZoneInPixels);
    this.context.moveTo(0, this.mapVisualizer.height);
    this.context.lineTo(deadZoneInPixels, this.mapVisualizer.height - deadZoneInPixels);
    this.context.lineTo(deadZoneInPixels, deadZoneInPixels);
    this.context.stroke();
    this.context.closePath();

    const positionInPixels = 
    {
      x: this.mapVisualizer.width / 2,
      y: this.mapVisualizer.height / 2,
    };

    const pointsZoneDimension = new PixelDimension(
      this.mapVisualizer.width - deadZoneInPixels * 2,
      this.mapVisualizer.height - deadZoneInPixels * 2
    );

    const circleRadius = this.mapVisualizer.height > this.mapVisualizer.width ? 
    (pointsZoneDimension.width * this.levelDiameter) / 100 / 2 : (pointsZoneDimension.height * this.levelDiameter) / 100 / 2;

    // circle
    this.context.beginPath();
    this.context.arc(positionInPixels.x,positionInPixels.y,circleRadius, 0, 2 * Math.PI, false);
    this.context.fillStyle = 'white';
    this.context.fill();
    this.context.lineWidth = 2;
    this.context.strokeStyle = 'black';
    this.context.stroke();
    this.context.closePath();

    // text
    this.context.fillStyle = 'black';
    this.context.font = this.levelDiameter / 4 + 'px Arial';
    this.context.textAlign = 'center';
    this.context.textBaseline = 'middle';
    this.context.fillText('L0', positionInPixels.x, positionInPixels.y);
  }

  async promptClearAllData(): Promise<void> 
  {
    if (
      await Alert.confirm(
        `Clear all data?`,
        'This will clear ALL local storage data, including from other apps that are using the local storage',
        'Yes, clear all data',
        'warning'
      )
    ) 
    {
      this.clearAllData();
    }
  }

  private clearAllData(): void 
  {
    this._areaService.reset(true);
    this._levelService.reset(true);
    this._mapService.reset(true);
    this._mapPointsService.reset(true);   
    this._userSettingsService.reset();
    localStorage.clear();
    this.calculateLocalStorage();
  }
  
  async promptClearAppData(): Promise<void> 
  {
    if (
      await Alert.confirm(
        `Are you sure you want to clear the app's data?`,
        'This will clear all unsaved data',
        `Yes, clear MapSys' data`
      )
    ) 
    {
      this.clearAppData();
    }
  }

  private clearAppData(): void 
  {
    this._dataService.clearData();
    this._router.navigate(['manager']);
  }

  preferencesWidth(dimensions : {width: number, height: number})
  {
    this.mapVisualizer.width = dimensions.width;
    this.mapVisualizer.height = dimensions.height;

    this.mapVisualizer.width = dimensions.width * 30;
    this.mapVisualizer.height = dimensions.height * 30;
    let deadzone = dimensions.height > dimensions.width ? dimensions.width * this.deadZoneSize/100 * 30 : dimensions.height * this.deadZoneSize/100 * 30;
    
    this.context.clearRect(0, 0,this.mapVisualizer.width, this.mapVisualizer.height);
    this.context.strokeStyle = 'white';
    this.context.beginPath();

    this.context.moveTo(0, 0);
    this.context.lineTo(deadzone, deadzone);
    this.context.lineTo(this.mapVisualizer.width - deadzone, deadzone);
    this.context.moveTo(this.mapVisualizer.width, 0);
    this.context.lineTo(this.mapVisualizer.width - deadzone, deadzone);
    this.context.lineTo(this.mapVisualizer.width - deadzone,this.mapVisualizer.height - deadzone);
    this.context.moveTo(this.mapVisualizer.width, this.mapVisualizer.height);
    this.context.lineTo(this.mapVisualizer.width - deadzone, this.mapVisualizer.height - deadzone);
    this.context.lineTo(deadzone, this.mapVisualizer.height - deadzone);
    this.context.moveTo(0, this.mapVisualizer.height);
    this.context.lineTo(deadzone, this.mapVisualizer.height - deadzone);
    this.context.lineTo(deadzone, deadzone);
    this.context.stroke();
    this.context.closePath();

    const pointsZoneDimension = new PixelDimension(
      this.mapVisualizer.width - deadzone * 2,
      this.mapVisualizer.height - deadzone * 2
    );

    const circleRadius = this.mapVisualizer.height > this.mapVisualizer.width ? 
    (pointsZoneDimension.width * this.levelDiameter) / 100 / 2 : (pointsZoneDimension.height * this.levelDiameter) / 100 / 2;

     // circle
     this.context.beginPath();
     this.context.arc(this.mapVisualizer.width/2, this.mapVisualizer.height/2, circleRadius, 0, 2 * Math.PI, false);
     this.context.fillStyle = 'white';
     this.context.fill();
     this.context.lineWidth = 2;
     this.context.strokeStyle = 'black';
     this.context.stroke();
     this.context.closePath();
 
     // text
     this.context.fillStyle = 'black';
     this.context.font = this.levelDiameter / 4 + 'px Arial';
     this.context.textAlign = 'center';
     this.context.textBaseline = 'middle';
     this.context.fillText('L0', this.mapVisualizer.width/2, this.mapVisualizer.height/2);
  }
}
