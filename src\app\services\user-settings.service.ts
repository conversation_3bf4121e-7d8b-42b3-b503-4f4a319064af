import { Injectable } from '@angular/core';
import { Ratio, UserSettings, ASPECT_RATIO_PRESETS } from '../../models/mapsys1';
import { getData, setData } from '../../custom/others';
import { Popup } from '../../custom/Popup';
import { Subject } from 'rxjs';
import { File } from '../../custom/File';
import { AreaService } from './area.service';
import { LevelService } from './level.service';

/**
 * Interface para configurações específicas do mapa
 */
interface MapSettings
{
  /** Ponto de pivô da viewport */
  viewPortPivot: { x: number; y: number };
  /** Valor do zoom */
  zoomValue: number;
}

/**
 * Serviço de configurações do usuário
 * Gerencia todas as preferências e configurações personalizáveis do sistema
 */
@Injectable({
  providedIn: 'root',
})
export class UserSettingsService
{
  /** Subject para notificar mudanças nas preferências */
  lastPreferencesChange: Subject<Date> = new Subject<Date>();

  /** Nome do tipo para armazenamento */
  typeName = 'UserSettings';
  /** ID do mapa atual */
  currentMapId : string;

  /** Dados das configurações do usuário */
  data: UserSettings;

  /**
   * Cria botões de popup para seleção de proporções
   * @param ratios Array de proporções para criar botões
   * @returns Array de botões de popup
   */
  ratioButtons(ratios: Ratio[]): Popup.Button<Ratio>[]
  {
    const buttons: Popup.Button<Ratio>[] = [];
    ratios.forEach((ratio) => {
      buttons.push(new Popup.Button(ratio.c + 'x' + ratio.a, ratio));
    });
    return buttons;
  }

  /**
   * Construtor do serviço de configurações
   * @param _areaService Serviço de áreas
   * @param _levelService Serviço de níveis
   */
  constructor(private _areaService: AreaService, private _levelService: LevelService)
  {
    this.loadData();
  }

  /**
   * Obtém as configurações do editor para um mapa específico
   * Cria configurações padrão se não existirem
   * @param mapId ID do mapa
   * @returns Configurações do editor para o mapa
   */
  getEditorConfigurations(mapId: string): MapSettings
  {
    this.currentMapId = mapId;

    if (!this.data.editorConfigurations[mapId])
    {
      this.data.editorConfigurations[mapId] =
      {
        viewPortPivot: { x: 0, y: 0 },
        zoomValue: 100,
      };
      this.saveData();
    }
    return this.data.editorConfigurations[mapId];
  }

  /**
   * Define uma configuração específica do editor para um mapa
   * @param mapId ID do mapa
   * @param configName Nome da configuração
   * @param value Valor da configuração
   */
  setEditorConfigurations(mapId: string, configName: string, value: any): void
  {
    this.getEditorConfigurations(mapId)[configName] = value;
    this.saveData();
  }

  setLevelDiameter(diameter: number): void 
  {
    if (diameter < 1) return;
    
    this.data.levelDiameter = diameter;
    this.saveData();
  }

  setDeadZoneSize(size: number): void 
  {
    this.data.deadZoneSize = size;
    this.saveData();
  }

  setHighlightLevelsInEditor(value: boolean) 
  {
    this.data.highlightLevelsInEditor = value;
    this.saveData();
  }

  setHermiteCurveThickness(hermiteCurveThickness: number) 
  {
    this.data.hermiteCurveThickness = hermiteCurveThickness;
    this.saveData();
  }

  setHermiteCurveTension(value: number) 
  {
    this.data.hermiteCurveTension = value;
    this.saveData();
  }

  setHermiteCurveColor(value: string) 
  {
    this.data.hermiteCurveColor = value;
    this.saveData();
  }

  setCurrentDrawColor(value: string) 
  {
    this.data.currentSelectedColor = value;
    this.saveData();
  }
  setCurrentDrawThickness(value: number) 
  {
    this.data.curveThickness = value;
    this.saveData();
  }

  getHermiteCurveThickness(): number 
  {
    return this.data.hermiteCurveThickness;
  }

  getHermiteCurveTension():number 
  {
    return this.data.hermiteCurveTension;
  }

  getHermiteCurveColor():string 
  {
    return this.data.hermiteCurveColor;
  }

  getCurrentDrawColor():string 
  {
    return this.data.currentSelectedColor;
  }
  getCurrentDrawThickness():number 
  {
    return this.data.curveThickness;
  }

  addAspectRatio(ratio: Ratio): number 
  {
    let index = -1;
    let indexOfReversedRatio = this.data.aspectRatios.indexOf(
      this.data.aspectRatios.find((r) => (r.a === ratio.c && r.c === ratio.a) || (r.a === ratio.a && r.c === ratio.c))
    );
    if (indexOfReversedRatio >= 0) 
    {
      this.data.aspectRatios[indexOfReversedRatio] = ratio;
      index = indexOfReversedRatio;
    } 
    else 
    {
      this.data.aspectRatios.push(ratio);
      index = this.data.aspectRatios.length - 1;
    }
    this.saveData();
    return index;
  }

  removeAspectRatio(ratio: Ratio): void 
  {
    this.data.aspectRatios.splice(this.data.aspectRatios.indexOf(ratio), 1);
    this.saveData();
  }

  setViewPortRatio(viewPortRatio: Ratio): void 
  {
    const indexNew = 0;
    const indexOld = this.addAspectRatio(viewPortRatio);
    const oldRatio = this.data.aspectRatios[indexOld];
    const newRatio = this.data.aspectRatios[indexNew];
    this.data.aspectRatios[indexOld] = newRatio;
    this.data.aspectRatios[indexNew] = oldRatio;

    this.saveData();
  }

  importData(data: UserSettings): void 
  {
    this.data = data;
    this.deepLoadData();
    this.changeSuffix();
    this.saveData();
  }

  exportData(): any 
  {
    return this.data;
  }

  detectDSADataFromLocalStorage(value: boolean): void 
  {
    this.data.detectDSADataFromLocalStorage = value;
    this.changeSuffix();
    this.saveData();
  }

  reset(): void 
  {
    this.data = 
    {
      aspectRatios: ASPECT_RATIO_PRESETS,
      detectDSADataFromLocalStorage: false,
      deadZoneSize: 15,
      levelDiameter: 15,
      hermiteCurveThickness: 1, 
      hermiteCurveColor: 'red',
      hermiteCurveTension: 0.5,
      currentSelectedColor: 'blue',
      curveThickness:1,
      editorConfigurations: {},
      highlightLevelsInEditor: true,
    };
    this.saveData();
  }

  private loadData(): void 
  {
    this.data = getData(this.typeName, File.MPS_SUFFIX_PATH) || 
    {
      highlightLevelsInEditor: true,
      aspectRatios: ASPECT_RATIO_PRESETS,
      detectDSADataFromLocalStorage: false,
      deadZoneSize: 15,
      levelDiameter: 15,
      hermiteCurveThickness: 1, 
      hermiteCurveColor: 'red',
      curveThickness: 1,
      hermiteCurveTension: 0.5,
      currentSelectedColor: 'blue',
      editorConfigurations: {},
    };

    if (!this.data.editorConfigurations) 
    {
      this.data.editorConfigurations = {};
    }

    this.deepLoadData();

    this.changeSuffix();
    this.saveData();
  }

  private deepLoadData(): void 
  {
    this.data.aspectRatios.forEach((ratio, index) => 
    {
      this.data.aspectRatios[index] = Ratio.deepLoad(ratio);
    });
  }

  private saveData(): void 
  {
    setData(this.data, this.typeName, File.MPS_SUFFIX_PATH);
    this.lastPreferencesChange.next(new Date());
  }

  private changeSuffix(): void 
  {
    if (this.data.detectDSADataFromLocalStorage) 
    {
      this._areaService.pathSuffix = File.DSA_SUFFIX_PATH;
      this._levelService.pathSuffix = File.DSA_SUFFIX_PATH;
    } 
    else 
    {
      this._areaService.pathSuffix = File.MPS_SUFFIX_PATH;
      this._levelService.pathSuffix = File.MPS_SUFFIX_PATH;
    }
  }
}
