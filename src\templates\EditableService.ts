import { ExportableClass } from '../models/mapsys1';
import { StaticService } from './StaticService';
import { Alert } from '../custom/Alert';
import { sortData, setFilterValue } from '../custom/others';

/**
 * Classe abstrata para serviços editáveis
 * Estende StaticService com funcionalidades de criação, edição e remoção
 */
export abstract class EditableService<
  T extends ExportableClass
> extends StaticService<T> {
  /**
   * Construtor do serviço editável
   * @param deepLoad Função que retorna um objeto vazio do tipo T
   * @param typeName Nome do tipo de objeto que este serviço gerencia
   * @param srtLstParameter Parâmetro para ordenação da lista (opcional)
   */
  constructor(
    /**
     * Uma função que retorna um objeto vazio do tipo T.
     */
    deepLoad: (obj: T) => T,
    /**
     * Nome do tipo de objeto que este serviço gerencia
     * @example 'Character'
     */
    typeName: string,
    /**
     * Parâmetro para ordenação da lista
     * @example 'name'
     */
    public srtLstParameter?: string
  ) {
    super(deepLoad, typeName);
  }

  /**
   * Obtém o próximo índice baseado na localização
   * @param locationId ID da localização
   * @returns Próximo índice disponível
   */
  public nextIndexByLocation(locationId: string): number {
    return this.data.filter((obj) => obj.id.includes(locationId + '.')).length;
  }

  /**
   * Método executado após salvar dados
   * Atualiza índices automaticamente
   */
  protected srvAfterSave(): void {
    this.UpdateIndexes();
  }

  /**
   * Atualiza índices dos objetos
   * @param objs Objetos para atualizar índices
   */
  public UpdateIndexes(...objs: T[]): void {}

  // Métodos de Modificação de Dados
  /**
   * Verifica se um objeto é válido antes de salvar
   * @param obj Objeto para verificar
   */
  protected srvVerify(obj: T): void {}

  /**
   * Substitui um objeto com o mesmo ID pelo objeto modificado
   * @param obj Objeto modificado que substituirá outro objeto com o mesmo ID
   */
  protected srvReplace(obj: T): void {
    this.data[this.indexOfId(obj.id)] = this.clone(obj);
  }

  /**
   * Remove objetos do array de dados
   * @param objIds IDs dos objetos a serem removidos
   * @returns Array de objetos removidos
   */
  public srvRemove(objIds: string[]): T[] {
    // this.lastRemoval.next(new ListRemoval(this, id, this.indexOfId(id)));
    const removedObjs = this.filterByIds(objIds);
    this.data = this.data.filter((o) => !objIds.includes(o.id));
    return removedObjs;
  }

  /**
   * Cria um objeto do tipo T
   * Método abstrato que deve ser implementado pelas classes filhas
   * @param args Argumentos para criação do objeto
   * @returns Objeto criado ou Promise do objeto
   */
  public abstract promptCreateNewMapPoints(...args: any): Promise<T> | T;

  /**
   * Solicita confirmação antes de remover um elemento da lista
   * @todo mover este método para nível de serviço como a função promptCreateNew()
   * @param obj Objeto a ser removido
   * @returns True se o objeto foi removido, false caso contrário
   */
  public async promptRemove(obj: T): Promise<boolean> {
    const confirm = await Alert.confirmRemove(obj.id);
    if (confirm) {
      this.remove(obj.id);
      return true;
    }
    return false;
  }

  // list methods
  /**
   * Switches places of two objects from the data array
   * @param index Index of the first element to be switched
   * @param otherIndex Index of the second element to be switched
   */
  public switchPlaces(index: number, otherIndex: number): void {
    if (this.data.length <= 1) {
      return;
    }
    const obj = this.data[index];
    const otherObj = this.data[otherIndex];
    this.data[index] = otherObj;
    this.data[otherIndex] = obj;
    this.save();
  }

  /**
   * Adds an object to the data array
   * @param obj Object to be added to the data array
   * @param index Optional parameter that specified what place in the data array to add the object
   */
  protected srvAdd(obj: T, index?: number): void {
    if (index !== undefined) {
      this.data = this.data
        .slice(0, index)
        .concat(obj)
        .concat(this.data.slice(index));
      // this.switchPlaces(obj.id, this.data[index].id);
    } else {
      this.data.push(obj);
    }
  }

  /**
   * Moves an object inside the data array
   * @param obj Object that will be moved
   * @param index Index of the data array to move the object to
   */
  public move(obj: T, index: number): void {
    if (index >= this.data.length) {
      return;
    }
    this.data = this.data.filter((o) => o.id !== obj.id);
    this.data = this.data
      .slice(0, index)
      .concat(obj)
      .concat(this.data.slice(index));
    this.modify(obj);
  }

  /**
   * Adds a new object to the data array, reviews it and saves it
   */
  public add(obj: T, index?: number): T {
    if (!obj) {
      return;
    }
    try {
      if (this.findById(obj.id)) {
        throw new Error('Object with the same id already exists: ' + obj.id);
      }
      this.srvAdd(obj, index);
      this.save();
      return obj;
    } catch (error) {
      Alert.showError(error);
    }
  }

  protected srvSave(): void {
    if (this.srtLstParameter) {
      this.data = sortData(this.data, this.srtLstParameter);
    }
    this.setSRVData(this.data);
    setFilterValue(this._nextIndex, this.typeName, 'nextIndex');
  }

  /**
   * Replaces the object that has the same ID with a new object
   * @param newObj Object that will replace the other object that has the same ID.
   */
  public modify(newObj: T): void 
  {
    try 
    {
      const oldObj = this.findById(newObj.id);
      if (!oldObj) return;
      
      this.srvVerify(newObj);
      this.srvReplace(newObj);
    } 
    catch (error) 
    {
      Alert.showError(error);
    }
    this.save();
  }

  /**
   * Triggers srvUnlink, srvRemove, Unreview and then Save in order.
   */
  public remove(objIds: string | string[]): void {
    const ids: string[] = [].concat(objIds);
    const removedObjs = this.srvRemove(ids);
    if (removedObjs.length === 0) {
      return;
    }
    this.srvUnlink(removedObjs);
    this.save();
    this.lastModifiedId = undefined;
  }

  /**
   * Method triggered after removing objects that ensures that any other object
   * that points to these objects' ids have those pointers removed
   * @example srvUnlink(levels: Level[]) removes the them from the levelIds of their parent area.
   * @param objs Objects which were removed
   */
  protected srvUnlink(objs: T[]): void {}

  /**
   * sets the next index and returns it
   */
  public nextIndex(): number {
    if (this._nextIndex) {
      ++this._nextIndex;
    } else {
      this._nextIndex = this.data.length + 1;
    }
    return this._nextIndex - 1;
  }
}
