import { ExportablePackage } from './ExportablePackage';
import { ExportableClass, Level } from '../models/mapsys1';
import { getInputList, setData, getData, setFilterValue, getFilterValue, getLastModified, setLastModified} from '../custom/others';
import { Alert } from '../custom/Alert';
import { ServiceLifeCycle } from './ServiceLifeCycle';
import { Subject } from 'rxjs';
import { File } from '../custom/File';

/**
 * Modelo de classe básica para um serviço que gerencia arrays de dados para classes exportáveis.
 */
export abstract class StaticService<T extends ExportableClass> extends ServiceLifeCycle<T> 
{
  /**
   * Um array que armazena objetos de classes exportáveis.
   */
  public data: T[] = [];

  /**
   * Um objeto que armazena o último objeto acessado do serviço
   */
  private _lastModifiedId: string;
  public lastIdModification: Subject<string> = new Subject<string>();

  public set lastModifiedId(id: string) {
    this._lastModifiedId = id;
    this.lastIdModification.next(id);
    setLastModified(id, this.typeName);
  }
  public get lastModifiedId(): string {
    if (!this._lastModifiedId) {
      this._lastModifiedId = getLastModified(this.typeName);
    }
    return this._lastModifiedId;
  }

  /**
   * Armazena o índice do próximo objeto
   */
  protected _nextIndex: number;

  /**
   * Cria uma instância de um Serviço e carrega automaticamente dados do Local Storage
   *
   * @param typeName O nome do tipo de Serviço (T)
   * @param srtLstParameter O nome da propriedade do objeto de dados que será usada para ordenar os dados ao salvá-los
   */
  public constructor(
    /**
     * Uma função que retorna um objeto vazio do tipo T.
     */
    private deepLoad: (obj: T) => T,
    /**
     * Nome do tipo de objeto que este serviço gerencia
     * @example 'Character'
     */
    public readonly typeName: string
  ) {
    super(typeName);
    this.load();
  }

  private _pathSuffix: string = File.MPS_SUFFIX_PATH;

  set pathSuffix(pathSuffix: string) 
  {
    if (this._pathSuffix !== pathSuffix) 
    {
      if (pathSuffix === File.DSA_SUFFIX_PATH) 
      {
        this.reset(true);
      }
      this.data = [];
      this.lastModifiedId = undefined;
      this._pathSuffix = pathSuffix;
      this.load();
    }
  }

  get pathSuffix(): string 
  {
    return this._pathSuffix;
  }

  /**
   * Define um array de dados no Local Storage
   */
  protected setSRVData(data: T[]): void
  {
    setData(data, this.typeName, this._pathSuffix);
  }
  /**
   * Recupera um array de dados do Local Storage
   */
  protected getSRVData(): T[]
  {
    return getData(this.typeName, this._pathSuffix) || [];
  }

  /**
   * Define data e removedData como um array vazio
   */
  public reset(forceLocalStorageReset?: boolean): void 
  {
    if (this._pathSuffix === File.MPS_SUFFIX_PATH || forceLocalStorageReset) 
    {
      this.data = [];
      this.lastModifiedId = undefined;
      this.srvSave();
    }
  }

  /**
   * Importa dados de um ExportablePackage e os salva automaticamente no Local Storage
   * @todo como isso já importa os dados e os atribui ao objeto,
   * não há necessidade de cópia profunda de objeto do Componente de Importação
   *
   * @param dataPackage O ExportablePackage a ser importado e gerenciado pelo Serviço.
   */
  public importPackage(dataPackage: ExportablePackage<T>): void 
  {
    this.reset();
    dataPackage.data.forEach((obj) => 
    {
      this.data.push(this.deepLoad(obj));
    });
    this.srvAfterLoad();
    this._nextIndex = dataPackage.nextIndex || this.data.length;
    this.srvSave();
  }

  /**
   * Exporta dados para um ExportablePackage
   *
   * @param exportedDateTime Uma string da data e hora de exportação para versionamento do ExportedPackage.
   * @param version Uma string da versão principal para versionamento do ExportedPackage.
   *
   * @returns Um ExportablePackage com dados de jogo de Classe Exportável.
   */
  public exportPackage(
    exportedDateTime: string,
    version: string
  ): ExportablePackage<T> {
    const dataPackage = new ExportablePackage<T>(
      exportedDateTime,
      version,
      this.data,
      [],
      this._nextIndex
    );
    return dataPackage;
  }

  protected srvLoad(): void {
    this.getSRVData().forEach((obj) => {
      this.data.push(this.deepLoad(obj));
    });
  }

  protected srvAfterLoad(): void {
    this._nextIndex = getFilterValue(this.typeName, 'nextIndex');
  }

  protected srvSave(): void {
    this.setSRVData(this.data);
    setFilterValue(this._nextIndex, this.typeName, 'nextIndex');
  }

  // métodos de busca de dados
  /**
   * Encontra o índice de um objeto dentro do array de dados e o retorna
   * @param id O id do objeto que será pesquisado
   */
  public indexOfId(id: string): number {
    const obj = this.findById(id);
    if (!obj) {
      return;
    }
    const index = this.data.indexOf(obj);
    return index === -1 ? undefined : index;
  }

  /**
   * Filtra o array de dados retornando objetos que contêm uma determinada string de localização no ID
   * @example
   * // data = [{id:'A1.L1'}, {id:'A2.L2'}, {id:'A2.L3'}]
   * // retorna [{id:'A2.L2'}, {id:'A2.L3'}]
   * filterByLocation('A2');
   * @param id O id do objeto que será pesquisado
   */
  public filterByLocation(locationId: string): T[] {
    return this.data.filter((o) => o.id.includes(locationId + '.'));
  }
  public cloneByLocation(locationId: string): T[] {
    const objs: T[] = [];
    this.data
      .filter((o) => o.id.includes(locationId + '.'))
      .forEach((o) => {
        objs.push(this.clone(o));
      });
    return objs;
  }

  /**
   * Filters the data array by IDs and returns it
   * @param ids IDs of the objects to be filtered
   */
  public filterByIds(ids: string[], inOrderOfIdString?: boolean): T[] 
  {
    if (!inOrderOfIdString) 
    {
      return this.data.filter((obj) => ids?.includes(obj.id));
    } 
    else 
    {
      const objs: T[] = [];
      ids.forEach((id) => 
      {
        const obj = this.data.find((o) => o.id === id);
        if (!obj) return;
        objs.push(obj);
      });
      return objs;
    }
  }

  /**
   * Clones the data array by IDs and returns it
   * @param ids IDs of the objects to be filtered
   */
  public cloneByIds(ids: string[], inOrderOfIdString?: boolean): T[] {
    const objs: T[] = [];
    if (!inOrderOfIdString) {
      this.data
        .filter((obj) => ids.includes(obj.id))
        .forEach((o) => {
          objs.push(this.clone(o));
        });
    } else {
      ids.forEach((id) => {
        const obj = this.findById(id);
        if (!obj) {
          return;
        }
        objs.push(this.clone(obj));
      });
    }
    return objs;
  }

  /**
   * Finds an object with a specific ID and returns it
   * @param ids ID of the object to be searched
   */
  public findById(id: string): T {
     return this.data.find((o) => o.id === id)
  }
/* o.id === id */
 
  /**
   * Clones an exact copy of the object but without the array reference by a specific ID and returns it
   * Use this instead of findById on lists where the user can modify the object and call
   * LifeCycleHooks.ServiceLifeCycle.modify method to replace the new object to the correspoding object with same id.
   * @param ids ID of the object to be searched
   * @see LifeCycleHooks.ServiceLifeCycle.modify
   */
  public cloneById(id: string): T {
    const obj = this.findById(id);
    if (!obj) {
      return;
    }
    return this.clone(obj);
  }

  public clone(obj: T): T {
    return this.deepLoad(JSON.parse(JSON.stringify(obj)));
  }

  public async selectId(
    orderByParameter: boolean,
    parameters: string[],
    customData?: T[]
  ): Promise<string> {
    const inputList = customData
      ? getInputList(customData, 'id', parameters, orderByParameter)
      : getInputList(this.data, 'id', parameters, orderByParameter);
    inputList.undefined = 'Undefined';
    return await Alert.showDropdown('Select ' + this.typeName, inputList).then(
      (res) => {
        return res;
      }
    );
  }

  protected srvAfterSave(): void {}
}
