import { Component, OnInit, HostListener, After<PERSON><PERSON>w<PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { Popup } from '../../../custom/Popup';
import { Index } from '../../../custom/others';
import { PopupService } from '../../../app/services/popup.service';

@Component({
  selector: 'app-popup',
  templateUrl: './popup.component.html',
  styleUrls: ['./popup.component.css']
})
export class PopupComponent implements OnInit, AfterViewInit, OnDestroy 
{
  public popupInterfaces: Popup.Interface<any, any>[];
  public popupElement: HTMLElement;
  public selectedButton: Index<Popup.Button<any>> = {};
  firsTimeout;

  @HostListener('document:click', ['$event'])
  onClick(event): void 
  {
    if (this.popupElement && !this.popupElement.contains(event.target)) 
    {
      this._popupService.close();
      return;
    }
  }

  constructor(private _popupService: PopupService) { }

  ngOnInit(): void 
  {
    this.popupInterfaces = [];
    this.popupInterfaces.push(this._popupService.popupInterface);
    if (this._popupService.popupInterface?.inputSettings?.inputButton) 
    {
      const buttonSelected =
        this._popupService.popupInterface.buttons
          .find(button => button.value === this._popupService.popupInterface.inputSettings.inputButton.value);
      if (buttonSelected) 
      {
        if (this.popupInterfaces[0]?.inputSettings?.next) 
        {
          this.selectedButton[0] = buttonSelected;
          const a = this.popupInterfaces[0].inputSettings.next(buttonSelected);
          this.popupInterfaces.push(a);
        }
      }
    }

   // this.changePopupInterfacesOrientation();
  }

  /* changePopupInterfacesOrientation()
  {
    for(let i = 0; i < this.popupInterfaces.length; i++)
    {
      for(let j = 0; j < this.popupInterfaces[i].buttons.length; j++)
      {
        let aux = this.popupInterfaces[i].buttons[j].value.a;
        this.popupInterfaces[i].buttons[j].value.a = this.popupInterfaces[i].buttons[j].value.c;
        this.popupInterfaces[i].buttons[j].value.a = aux;
      }
    }

  } */
  

  ngAfterViewInit(): void 
  {
   this.firsTimeout = setTimeout(() => 
   {
      this.popupElement = document.getElementById('popup');
    }, 100);
  }

  public ClickButton(interfaceIndex: number, buttonIndex: number): void 
  {
    if (this.popupInterfaces[interfaceIndex]?.inputSettings?.next) 
    {
      if (this.popupInterfaces.length > interfaceIndex + 1)
      {
        this.popupInterfaces.splice(interfaceIndex + 1);
      }
      const button = this.popupInterfaces[interfaceIndex].buttons[buttonIndex];
      this.selectedButton[interfaceIndex] = button;
      const a = this.popupInterfaces[interfaceIndex].inputSettings.next(button);
      this.popupInterfaces.push(a);

    } 
    else 
    { 
      this._popupService.close(this.popupInterfaces[interfaceIndex].buttons[buttonIndex]); 
    }
  }

  ngOnDestroy(): void 
  {
    clearTimeout(this.firsTimeout);
  }

}
