import { Ratio } from './ScreenProperties';
import { LevelPoint } from './LevelPoint';
import { ExportableClass } from './ExportableClass';
import { prefixes } from './EnumMaps';

/**
 * Classe que representa um mapa no sistema
 * Contém informações sobre área, proporção de aspecto e pontos dos níveis
 */
export class Map extends ExportableClass
{
  /**
   * Construtor da classe Map
   * @param areaId ID da área associada ao mapa
   * @param index Índice para geração do ID único
   * @param aspectRatio Proporção de aspecto do mapa
   */
  constructor(public areaId: string, index: number, public aspectRatio: Ratio)
  {
    super(Map.generateId(index));
  }

  /** Dicionário de pontos dos níveis indexados por ID do nível */
  points:
  {
    [levelId: string]: LevelPoint;
  } = {};

  /**
   * Carrega profundamente um mapa a partir de dados serializados
   * @param map Dados do mapa para carregar
   * @returns Instância de Map com dados carregados
   */
  static deepLoad(map: Map): Map
  {
    const deepLoadedMap = Object.assign(new Map(undefined, undefined, undefined), map);
    deepLoadedMap.aspectRatio = Ratio.deepLoad(map.aspectRatio);
    return deepLoadedMap;
  }

  /**
   * Gera um ID único para o mapa
   * @param index Índice para geração do ID
   * @returns ID único do mapa
   */
  public static generateId(index: number): string
  {
    return prefixes.MAP + index;
  }
}
