import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Alert } from '../../../../../custom/Alert';
import { Ratio } from '../../../../../models/mapsys1';

@Component({
  selector: 'app-input-modal',
  templateUrl: './input-modal.component.html',
  styleUrls: ['./input-modal.component.scss']
})
export class InputModalComponent {
  @Input() map: any;
  @Input() viewPort: any;
  @Output() closeModal = new EventEmitter<void>();
  @Output() applyRatio = new EventEmitter<{height: number, aspectRatio: any}>();

  isMapRatioModalOpen: boolean = true;

  closeMapRatioModal(): void {
    this.isMapRatioModalOpen = false;
    this.closeModal.emit();
  }

  applyMapRatio(heightValue: string, screenCountValue: string): void {
    // Check if we have valid input in at least one field
    const height = heightValue ? parseFloat(heightValue) : null;
    const screenCount = screenCountValue ? parseFloat(screenCountValue) : null;
    
    if (!height && !screenCount) {
      Alert.showError('Please enter either height or number of screens');
      return;
    }
    
    let newHeight: number;
    
    // Calculate height based on which field was filled
    if (height) {
      // Direct height input
      newHeight = height;
    } else if (screenCount) {
      // Calculate height based on screen count and viewport ratio
      newHeight = screenCount * (this.viewPort?.ratio?.a / this.viewPort?.ratio?.c) * this.map.aspectRatio.c;
    } else {
      return; // Shouldn't reach here due to earlier validation
    }
    
    // Validate the calculated height
    if (isNaN(newHeight) || newHeight <= 0) {
      Alert.showError('Invalid height value');
      return;
    }
    
    const c = this.map.aspectRatio.c;
    
    if (!c) {
      Alert.showError('Invalid map aspect ratio');
      return;
    }
    
    // Create new aspect ratio and emit it
    const newAspectRatio = new Ratio(newHeight, c);
    this.applyRatio.emit({height: newHeight, aspectRatio: newAspectRatio});
    
    // Close the modal
    this.closeMapRatioModal();
  }
}

