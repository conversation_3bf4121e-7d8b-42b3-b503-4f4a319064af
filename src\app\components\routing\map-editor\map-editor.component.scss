 .view-port-wrapper 
 {
  position: absolute;
  left: 35px;
  bottom: 15px;
}

#view-port 
{
  border: 3px solid white;
  z-index: 50;
  background-color: rgb(221, 221, 221);
}
.blue-color
{
  //background-color:rgb(69, 148, 238);
  border-color:rgb(200, 200, 200);

  // === EFEITO DE PROFUNDIDADE NO LADO DIREITO ===
  position: relative;
  box-shadow:
   // 3px 0 6px rgba(0, 0, 0, 0.2),        // Sombra principal no lado direito
    3px 2px 8px rgba(0, 0, 0, 0.15),     // Sombra secundária para mais profundidade
    inset -1px 0 2px rgba(255, 255, 255, 0.3); // Luz interna para realce

  // Efeito ao passar o mouse
  &:hover {
    box-shadow:
      4px 0 8px rgba(0, 0, 0, 0.25),      // Sombra mais intensa no hover
      4px 3px 10px rgba(0, 0, 0, 0.2),    // Sombra secundária mais pronunciada
      inset -1px 0 3px rgba(255, 255, 255, 0.4); // Luz interna mais brilhante

  //  transform: translateX(-1px);          // Leve movimento para a esquerda
  }

  // Efeito ao clicar
  &:active {
    box-shadow:
      2px 0 4px rgba(0, 0, 0, 0.3),       // Sombra reduzida no clique
      2px 1px 6px rgba(0, 0, 0, 0.2),     // Sombra secundária reduzida
      inset -1px 0 2px rgba(255, 255, 255, 0.2); // Luz interna reduzida

  //  transform: translateX(1px);           // Movimento para simular pressão
  }
}
  // Efeito ao passar o mouse
.effect-btn:hover {
    box-shadow:
      4px 0 8px rgba(0, 0, 0, 0.25),      // Sombra mais intensa no hover
      4px 3px 10px rgba(0, 0, 0, 0.2),    // Sombra secundária mais pronunciada
      inset -1px 0 3px rgba(255, 255, 255, 0.4); // Luz interna mais brilhante

  //  transform: translateX(-1px);          // Leve movimento para a esquerda
  }

  .btn-border-style {
    border-color: rgb(52, 114, 247) !important;
  }

.slider 
{
  width: 30%;
  z-index: 999;
}
.curve-thickness
{
  display: flex;
  flex-direction: row;
  margin-left: 5vw;
  gap:10px;
  align-items: center;
}
#map-canvas 
{
  position: absolute;
  display: none;
  left: 15%;
}
#editor-port 
{
  position: absolute;
  left: 15%;
  z-index: 20;
  background-color: rgb(221, 221, 221);
  color: white
}
#draw-canvas 
{
  position: absolute;
  z-index: 100;
  //border: 3px solid rgb(43, 7, 7);
  left: 15%; 
}
#draw-text-image
{
  position: absolute;
  z-index: 110;
  //border: 3px solid rgb(255, 238, 0);
  left: 15%; 
}

#item-canvas 
{
  position: absolute;
  z-index: 110;
  //border: 3px solid rgb(255, 238, 0);
  left: 15%; 

}

.slider-group 
{
  position: relative;
  display: -webkit-inline-box;
  z-index: 60;
}

.btn-Listdevice {
    margin-top: 5px;
    padding: 5px;
    box-shadow: 2px 2px #7d7d7d;
    border: 1px solid rgb(151,151,151);
    border-radius: 5px;
    background-color: #8cd700;
    color: #fff;
}

.draw-section
{
  display: flex;
  flex-direction: column;
 /*  margin: -50px; */
}

.draw-image
{
  display: flex;
  flex-direction: row;
  align-content: space-between;
  gap: 10px;
  margin-left: 4vw;
}

// prevent unwanted dragging in chromium
div 
{
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#clipboard-image 
{
  position: absolute;
  opacity: 0;
  height: 100%;
  width: 100%;
  z-index: 25;
} 

.middle-window
{
  display: flex;
  justify-content: center;
  align-items: flex-start; // Alinha no topo para permitir scroll completo
  position: relative;
  height: 100vh;
  width: 100%;
  min-height: 600px;
  overflow: auto; // Permite rolagem automática quando necessário
  background-color: #f5f5f5; // Fundo sutil para destacar o mapa
  padding: 20px 0; // Adiciona padding para espaçamento
}

// Container centralizado para o mapa
.map-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-start; // Alinha no topo para mapas altos
  padding: 20px;
  min-height: 100%; // Garante altura mínima
  width: 100%; // Garante largura total
  margin-bottom: 90px;

  // Wrapper que mantém canvas e div de interação juntos
  .canvas-wrapper {
    position: relative;
    display: inline-block;

    // Garante que mapas muito altos sejam totalmente visíveis
    max-width: 100%;
    max-height: none; // Remove limitação de altura

    // Canvas principal
    #unified-canvas {
      border: 2px solid #999;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      background-color: #DDDDDD;
      transition: box-shadow 0.3s ease;
      display: block;

      &:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.2);
      }
    }

    // === DIV DE INTERAÇÃO PERFEITAMENTE ALINHADA ===
    #interaction-layer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
      cursor: crosshair;
      pointer-events: auto;
      border-radius: 8px; // Mesmo border-radius do canvas
    }
  }
}

.unified-canvas-map {
  display: block;
  position: relative;
  z-index: 1;
  background-color: #DDDDDD;
  border: 2px solid #999;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}



// === ESTILOS MOVIDOS PARA DENTRO DO .map-container ===
// A div de interação agora é estilizada dentro do contexto do container

// === ESTILOS DA BARRA DE ROLAGEM ===
// Barra de rolagem personalizada para a área do mapa
.middle-window::-webkit-scrollbar
{
  width: 12px;
  height: 12px;
}

.middle-window::-webkit-scrollbar-track
{
  background: #f1f1f1;
  border-radius: 6px;
}

.middle-window::-webkit-scrollbar-thumb
{
  background: #c1c1c1;
  border-radius: 6px;
  border: 2px solid #f1f1f1;
}

.middle-window::-webkit-scrollbar-thumb:hover
{
  background: #a8a8a8;
}

.middle-window::-webkit-scrollbar-corner
{
  background: #f1f1f1;
}

// === SUPORTE PARA MAPAS MUITO ALTOS ===
// Garante que mapas com altura muito grande sejam totalmente visíveis
.middle-window {
  // Quando o conteúdo é maior que a viewport
  &:has(.canvas-wrapper) {
    // Garante que o scroll funcione corretamente
    scroll-behavior: smooth;

    .map-container {
      // Remove limitações que podem cortar o conteúdo
      max-height: none;
      height: auto;

      // Garante que o container se expanda conforme necessário
      flex-shrink: 0;
    }
  }
}

// ===== ESTILOS PARA LINHAS DE NÍVEIS =====
.level-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 0px; // ✅ REMOVE PADDING PARA ALINHAR À ESQUERDA
  gap: 20px;
  border-bottom: 1px solid rgb(192, 192, 192);
  width: 300px;
  margin-bottom: 3px;
  margin-top: 3px;
  min-height: 50px; // Garante altura mínima para acomodar o círculo
  padding-left: 5px;
}

// ===== CONTAINER DO CÍRCULO =====
.circle-container {
  flex-shrink: 0; // Impede que o container do círculo seja comprimido
  display: flex;
  align-items: center;
  justify-content: flex-start; // ✅ ALINHA CÍRCULO À ESQUERDA
  margin-left: 0 !important; // ✅ FORÇA MARGEM ZERO
  padding-left: 0 !important; // ✅ FORÇA PADDING ZERO
}

// ===== TEXTO DO NOME DO NÍVEL =====
.level-name-text {
  flex: 1; // Permite que o texto ocupe o espaço restante
  min-width: 0; // Permite que o texto seja truncado se necessário
  word-wrap: break-word; // Quebra palavras longas
  overflow-wrap: break-word; // Compatibilidade adicional
  hyphens: auto; // Adiciona hífens automáticos
  line-height: 1.2; // Melhora a legibilidade

  // ✅ REMOVE QUALQUER ESTILO DE BOTÃO
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  outline: none !important;
  text-decoration: none !important;
  cursor: inherit !important; // Herda o cursor do elemento pai

  // ✅ MANTÉM ESTILO DE TEXTO SIMPLES
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  text-align: left; // Alinha texto à esquerda
}

// ===== ESTILOS DOS CÍRCULOS =====
.alignLevels-circle {
  margin-left: 0px !important; // ✅ REMOVE MARGEM PARA ALINHAR À ESQUERDA
  font-size: 15px;
  // ✅ FORÇA DIMENSÕES FIXAS PARA MANTER FORMATO REDONDO
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  max-width: 40px !important;
  max-height: 40px !important;
  flex-shrink: 0 !important; // Impede compressão
  flex-grow: 0 !important;   // Impede expansão
  box-sizing: border-box !important; // Inclui bordas no cálculo do tamanho
}

.align-circle {
   margin-left: 0px !important; // ✅ REMOVE MARGEM PARA ALINHAR À ESQUERDA
   font-size: 15px;
   // ✅ FORÇA DIMENSÕES FIXAS PARA MANTER FORMATO REDONDO
   width: 40px !important;
   height: 40px !important;
   min-width: 40px !important;
   min-height: 40px !important;
   max-width: 40px !important;
   max-height: 40px !important;
   flex-shrink: 0 !important; // Impede compressão
   flex-grow: 0 !important;   // Impede expansão
   box-sizing: border-box !important; // Inclui bordas no cálculo do tamanho
}

// ===== OVERRIDE GLOBAL PARA CÍRCULOS NO MAP-EDITOR =====
// Garante que todos os círculos mantenham formato redondo fixo
.circle {
  // ✅ FORÇA DIMENSÕES FIXAS PARA TODOS OS CÍRCULOS
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  max-width: 40px !important;
  max-height: 40px !important;
  flex-shrink: 0 !important; // Impede compressão pelo flex
  flex-grow: 0 !important;   // Impede expansão pelo flex
  box-sizing: border-box !important; // Inclui bordas no cálculo
  border-radius: 50% !important; // Garante formato redondo
  display: flex !important; // Usa flex para centralizar conteúdo
  align-items: center !important; // Centraliza verticalmente
  justify-content: center !important; // Centraliza horizontalmente
  text-align: center !important; // Centraliza texto
  line-height: 1 !important; // Ajusta altura da linha para melhor centralização
  overflow: hidden !important; // Esconde qualquer conteúdo que exceda
  transition: border 0.2s ease !important; // Transição suave para a borda
}

// ===== DESTAQUE AMARELO PARA CÍRCULOS =====
.highlighted-circle {
  border: 3px solid #FFD700 !important; // Borda amarela dourada
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.6) !important; // Sombra amarela sutil
}

// ===== EFEITO HOVER PARA LINHAS DE NÍVEIS =====
.level-row {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 215, 0, 0.1) !important; // Fundo amarelo muito sutil

    .circle {
      border: 3px solid #FFD700 !important; // Borda amarela no hover
      box-shadow: 0 0 8px rgba(255, 215, 0, 0.6) !important; // Sombra amarela
    }
  }
}

.flex-container 
{
  display: flex;
  flex-direction: row;
}

span {
  font-size: 15px;
}

.div-sideBarModbile {
  display:flex; 
  flex-direction: column; 
  box-shadow: 2px 8px 8px 0px #888888;  
 // padding: 20px; 
  padding-top: 0;
  height: 100%;
  overflow: auto;
  width: 380px;
}

  .phone-frame {
    border: 5px solid #333;
    border-radius: 36px;
    overflow: hidden;
    background-color: #333;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

    .phone-top {
      height: 20px;
      background-color: #111;
      position: relative;

      .phone-notch {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 20px;
        background-color: #111;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
      }
    }

    .phone-bottom {
      height: 40px;
      background-color: #111;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: -6px;

      .home-button {
        width: 40px;
        height: 5px;
        background-color: #777;
        border-radius: 5px;
      }
    }
  }

  .title
{
    font-size: 30px;
    display: flex;
    align-self: center;
}

.p-hermite {
align-self: center;
padding-top: 10px;
height: 30px;
};
.color-line {
  background-color: #FBF3C1;
  padding-left: 5px;
/*  justify-content: space-between;
  text-align: -webkit-right;*/
}

// === RÉGUA SIMPLES COMO NO MODELO ===
.simple-ruler-container {
  position: relative;
  display: inline-block;
  width: 300px;
  height: 30px;
  background: transparent;
  padding: 5px 0;

  // === LINHA BASE DA RÉGUA ===
  .ruler-base-line {
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #000;
    z-index: 1;
  }

  // === MARCAÇÕES PRINCIPAIS (NÚMEROS INTEIROS) ===
  .main-tick {
    position: absolute;
    top: 10px;
    width: 1px;
    height: 20px;
    background: #000;
    transform: translateX(-50%);
    z-index: 2;
  }

  // === MARCAÇÕES MENORES (DÉCIMOS) ===
  .minor-tick {
    position: absolute;
    top: 15px;
    width: 1px;
    height: 10px;
    background: #000;
    transform: translateX(-50%);
    z-index: 2;
  }

  // === NÚMEROS DA RÉGUA ===
  .ruler-numbers {
    position: absolute;
    top: 32px;
    left: 0;
    width: 100%;
    height: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;

    .ruler-number {
      font-size: 12px;
      font-weight: normal;
      color: #000;
      text-align: center;
      min-width: 10px;
      line-height: 1;
    }
  }
}

// === SLIDER TRANSPARENTE SOBREPOSTO ===
.simple-ruler-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 45px;
  background: transparent;
  outline: none;
  position: absolute;
  top: 5px;
  left: 0;
  margin: 0;
  cursor: pointer;
  z-index: 3;

  // === TRACK INVISÍVEL ===
  &::-webkit-slider-track {
    width: 100%;
    height: 1px;
    background: transparent;
    border-radius: 0;
    border: none;
  }

  &::-moz-range-track {
    width: 100%;
    height: 1px;
    background: transparent;
    border-radius: 0;
    border: none;
  }

  // === THUMB (INDICADOR VERMELHO) ===
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 2px;
    height: 30px;
    background: #ff0000;
    cursor: pointer;
    border-radius: 0;
    border: none;
    position: relative;
    z-index: 4;
    margin-top: -15px;
  }

  &::-moz-range-thumb {
    width: 2px;
    height: 30px;
    background: #ff0000;
    cursor: pointer;
    border-radius: 0;
    border: none;
    position: relative;
    z-index: 4;
  }

  // === EFEITO HOVER ===
  &:hover::-webkit-slider-thumb {
    background: #cc0000;
  }

  &:hover::-moz-range-thumb {
    background: #cc0000;
  }
}

.ruler-wrapper {
  position: relative;
  width: 100%;
  height: 40px;
  margin: 15px 0;
}

.precise-ruler-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 30px;
  outline: none;
  background-color: transparent;
  margin: 0;
  padding: 0;
  
  // Fundo da régua com marcações
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background-image: 
      // Marcações principais (números)
      linear-gradient(to right, #000 1px, transparent 1px),
      // Marcações médias
      linear-gradient(to right, transparent 9.09%, #000 9.09%, transparent 9.1%),
      linear-gradient(to right, transparent 18.18%, #000 18.18%, transparent 18.19%),
      linear-gradient(to right, transparent 27.27%, #000 27.27%, transparent 27.28%),
      linear-gradient(to right, transparent 36.36%, #000 36.36%, transparent 36.37%),
      linear-gradient(to right, transparent 45.45%, #000 45.45%, transparent 45.46%),
      linear-gradient(to right, transparent 54.54%, #000 54.54%, transparent 54.55%),
      linear-gradient(to right, transparent 63.63%, #000 63.63%, transparent 63.64%),
      linear-gradient(to right, transparent 72.72%, #000 72.72%, transparent 72.73%),
      linear-gradient(to right, transparent 81.81%, #000 81.81%, transparent 81.82%),
      linear-gradient(to right, transparent 90.9%, #000 90.9%, transparent 90.91%),
      linear-gradient(to right, transparent 99.99%, #000 99.99%, transparent 100%),
      // Marcações pequenas (5 entre cada número)
      repeating-linear-gradient(to right, transparent, transparent 1.818%, #000 1.818%, #000 1.82%, transparent 1.82%, transparent 9.09%);
    background-size: 100% 100%, 100% 10px, 100% 10px, 100% 10px, 100% 10px, 100% 10px, 
                     100% 10px, 100% 10px, 100% 10px, 100% 10px, 100% 10px, 100% 10px, 100% 5px;
    background-position: 0 bottom, 0 bottom, 0 bottom, 0 bottom, 0 bottom, 0 bottom,
                         0 bottom, 0 bottom, 0 bottom, 0 bottom, 0 bottom, 0 bottom, 0 bottom;
    background-repeat: no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat,
                       no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, repeat-x;
    border-bottom: 1px solid #000;
  }
  
  // Números da régua
  &::after {
    content: "0 1 2 3 4 5 6 7 8 9 10";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #000;
    padding: 0 0 0 0;
  }
  
  // Estilo do cursor (thumb)
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 2px;
    height: 30px;
    background: #d32f2f;
    cursor: pointer;
    border: none;
    border-radius: 0;
    margin-top: -5px;
  }
  
  &::-moz-range-thumb {
    width: 2px;
    height: 30px;
    background: #d32f2f;
    cursor: pointer;
    border: none;
    border-radius: 0;
  }
  
  // Esconde a track padrão
  &::-webkit-slider-runnable-track {
    height: 0;
    background: transparent;
    border: none;
  }
  
  &::-moz-range-track {
    height: 0;
    background: transparent;
    border: none;
  }
}
// FIM DA REGUA
.thickness
{
    display: flex;
    flex-direction: column;
    align-items: center;
}

