import { ExportableClass } from './ExportableClass';
import { LevelType, prefixes } from './EnumMaps';
import { Area } from './Area';

/**
 * Nível do Jogo
 * Representa um nível individual no sistema de mapas
 */
export class Level extends ExportableClass
{
  /** Nome do nível */
  name: string;
  /** Descrição do nível */
  description: string;
  /** Tipo do nível (MINION, BOSS, etc.) */
  type: LevelType;
  /** IDs dos personagens que falam neste nível */
  speakerIds: string[] = [];
  /** IDs dos personagens de batalha neste nível */
  battleCharacterIds: string[] = [];
  /** IDs dos níveis conectados a este */
  linkedLevelIds: string[] = [];
  /** IDs dos diálogos associados a este nível */
  dialogueIds: string[] = [];

  /**
   * Construtor da classe Level
   * @param index Índice do nível na área
   * @param areaId ID da área que contém este nível
   */
  constructor(index: number, areaId: string)
  {
    super(Level.generateId(areaId, index));
    this.type = LevelType.MINION;
  }

  /**
   * Carrega profundamente um nível a partir de dados serializados
   * @param level Dados do nível para carregar
   * @returns Instância de Level com dados carregados
   */
  static deepLoad(level: Level): Level
  {
    return Object.assign(new Level(undefined, undefined), level);
  }

  /**
   * Gera um ID único para o nível
   * @param areaId ID da área
   * @param index Índice do nível
   * @returns ID único do nível no formato "areaId.LVLindex"
   */
  public static generateId(areaId: string, index: number): string
  {
    return areaId + '.' + prefixes.LEVEL + index;
  }

  /**
   * Obtém o sub-ID de um nível a partir de outro ID
   * @param otherId ID de referência
   * @returns Sub-ID extraído
   */
  public static getSubIdFrom(otherId: string): string
  {
    return this.getSubId(this.generateId(Area.generateId(0), 0), otherId);
  }
}
