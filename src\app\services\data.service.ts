import { Injectable } from '@angular/core';
import { AppService } from './app.service';
import { AreaService } from './area.service';
import { LevelService } from './level.service';
import { MapService } from './map.service';
import { MapPointsService } from './map-points.service';
import { UserSettingsService } from './user-settings.service';
import { File } from '../../custom/File';
import { ItemService } from './item.service';
import { EventService } from './event.service';

/**
 * Serviço de gerenciamento de dados
 * Responsável por importação, exportação e manipulação de dados do sistema
 * Coordena operações entre diferentes serviços de dados
 */
@Injectable({
  providedIn: 'root'
})
export class DataService
{

  /**
   * Construtor do serviço de dados
   * Injeta todos os serviços necessários para gerenciamento de dados
   */
  constructor(
    private _areaService : AreaService,
    private _levelService: LevelService,
    private _mapService: MapService,
    public _mapPointsService: MapPointsService,
    private _appService: AppService,
    private _userSettingsService: UserSettingsService,
    private _itemService : ItemService,
    private _eventService : EventService,
  ) { }
  
  /**
   * Importa dados MPS (Map Point System)
   * Carrega mapas, pontos de mapa e configurações do usuário
   * @param mps Arquivo MPS1 para importar
   */
  importMps(mps: File.MPS1)
  {
    const loadTime = new Date();

    this._mapService.importPackage(mps.mapPackage);
    this._mapPointsService.importPackage(mps.mapPointsPackage);
    this._userSettingsService.importData(mps.userSettings);
    this._appService.setMPSLastLoadedTime(loadTime);
  }

  /**
   * Exporta dados MPS (Map Point System)
   * Cria arquivo com mapas, pontos e configurações atuais
   * @returns Arquivo MPS1 para download
   */
  exportMps()
  {
    const dateTime = new Date();
    const exportedDateTime = dateTime.toLocaleDateString();

    const file = new File.MPS1(null, exportedDateTime, this._appService, this._mapService,
      this._mapPointsService, this._userSettingsService.data);

    return file;
  }


  /**
   * Importa dados DSA (Dragon Slayer Adventure)
   * Carrega áreas, níveis, pontos de mapa, itens e eventos
   * @param dsa Arquivo DSA para importar
   */
  importDsa(dsa: File.DSA)
  {
    const loadTime = new Date();
    this._areaService.importPackage(dsa.myAreaPackage);
    this._levelService.importPackage(dsa.myLevelPackage);
    this._mapPointsService.importPackage(dsa.myMapPointsPackage);
    this._itemService.importPackage(dsa.myItemPackage);
    this._eventService.importPackage(dsa.myEventPackage);
    this._appService.setDSALastLoadedTime(loadTime);
  }

  /**
   * Limpa todos os dados do sistema
   * Reseta todos os serviços para estado inicial
   */
  clearData()
  {
    this._areaService.reset();
    this._levelService.reset();
    this._itemService.reset();
    this._eventService.reset();
    this._mapService.reset();
    this._mapPointsService.reset();
    this._userSettingsService.reset();
    this._appService.setMPSLastLoadedTime(undefined);
    this._appService.setDSALastLoadedTime(undefined);
  }

  /**
   * Obtém todos os dados do sistema
   * @returns Objeto contendo dados de todos os serviços
   */
  getData()
  {
    return {
      areaData: this._areaService.data,
      levelData: this._levelService.data,
      itemData: this._itemService.data,
      eventData: this._eventService.data,
      mapData: this._mapService.data,
      mapPointsData: this._mapPointsService.data,
      userSettings: this._userSettingsService.data
    };
  }

}
