import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { Popup } from '../../custom/Popup';

@Injectable({
  providedIn: 'root',
})
export class PopupService 
{
  private res: Subject<Popup.Button<any>>;
  public popupInterface: Popup.Interface<any, any>;
  popping: Subject<boolean> = new Subject<boolean>();

  constructor() {}

  async fire<T, G>(popupInterface: Popup.Interface<T, G>): Promise<Popup.Button<G>> 
  {
    if (popupInterface.buttons.length > 0) 
    {
      this.popupInterface = popupInterface;
      this.popping.next(true);
      this.res = new Subject<Popup.Button<G>>();
      return await this.res.toPromise().then((res) => 
      {
        return res;
      });
    }
  }

  public close(button?: Popup.Button<any>): void 
  {
    this.popping.next(false);
    this.popupInterface = undefined;
    this.res.next(button);
    this.res.complete();
  }
}
