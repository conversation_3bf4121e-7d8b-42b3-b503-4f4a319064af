import { Injectable } from '@angular/core';
import { Level, LevelPoint, Ratio } from '../../models/mapsys1';
import { AreaService } from './area.service';
import { LevelService } from './level.service';
import { MapService } from './map.service';
import { UserSettingsService } from './user-settings.service';

import { Map } from '../../models/mapsys1';

/**
 * Interface para configurações do mapa
 */
interface MapSettings
{
  /** Ponto de pivô da viewport */
  viewPortPivot: { x: number; y: number };
  /** Valor do zoom */
  zoomValue: number;
}

/**
 * Serviço do editor de mapas
 * Responsável por operações de criação, edição e manipulação de mapas e pontos
 */
@Injectable({
  providedIn: 'root'
})
export class EditorService {

  /**
   * Construtor do serviço do editor
   * @param areaService Serviço de áreas
   * @param mapService Serviço de mapas
   * @param userSettingsService Serviço de configurações do usuário
   * @param levelService Serviço de níveis
   */
  constructor(
    public areaService: AreaService,
    public mapService: MapService,
    public userSettingsService: UserSettingsService,
    public levelService: LevelService) { }

  /**
   * Cria um novo mapa para uma área específica
   * Gera automaticamente posições para todos os níveis da área
   * @param areaId ID da área para criar o mapa
   */
  newMap(areaId: string)
  {
    // Ordena os níveis da área
    this.areaService.sortLevels(areaId);
    let userSettings : MapSettings = this.userSettingsService.getEditorConfigurations(areaId)

    // Cria o mapa com proporção baseada nas configurações do usuário
    const map = this.mapService.promptCreateNewMapPoints(
      areaId,
      new Ratio(userSettings.viewPortPivot.x, userSettings.viewPortPivot.y)
    );
    const radius = this.userSettingsService.data.levelDiameter * 0.5;
    const levelIds = this.areaService.findById(areaId).levelIds;

    // Configurações de layout
    const levelsPerPage = 10;
    const pages = levelIds.length / levelsPerPage;
    const minHeight = 16;
    const modularHeight = minHeight * (pages * 0.5);

    // Define o tamanho do mapa baseado no número de níveis
    const size : Ratio = new Ratio(
      (modularHeight > minHeight) ? modularHeight : minHeight,
      9
    );

    map.aspectRatio = size;

    // Define zoom padrão para 50%
    this.userSettingsService.getEditorConfigurations(map.id).zoomValue = 50;

    // Posição inicial para colocação dos níveis
    let position = new LevelPoint({xd: 0, yd: 100});
    let pageItems = 0;

    // Distribui os níveis automaticamente no mapa
    levelIds.forEach(level => {
      // Calcula multiplicadores para posicionamento
      let xMultiplier = (position.position.xd == 0) ? 1 : 0;
      let yMultiplier = (position.position.yd == 100) ? -1 : 0;
      let xOffset = 90/(levelsPerPage/2);

      // Cria ponto para o nível com posição calculada
      map.points[level] = new LevelPoint({
        xd: position.position.xd + radius * xMultiplier,
        yd: position.position.yd + (radius/1.75) * yMultiplier
      });
      pageItems++;

      // Ajusta posição horizontal baseado na página
      if(pageItems <= levelsPerPage * 0.5)
      {
        position.position.xd += (xOffset);
      }
      if(pageItems > levelsPerPage * 0.5)
      {
        position.position.xd -= (xOffset);
      }
      if(pageItems >= levelsPerPage)
        pageItems = 0;

      // Move para baixo para próxima linha
      position.position.yd -= (10/pages);
    });

    // Adiciona o mapa ao serviço
    this.mapService.add(map);
  }

  /**
   * Obtém o nível pai de um nível específico
   * Procura por um nível que tenha o levelId em seus linkedLevelIds
   * @param levelId ID do nível para encontrar o pai
   * @returns Nível pai ou undefined se não encontrado
   */
  getLevelParent(levelId: string)
  {
    for(let i = 0; i < this.levelService.data.length; i++){
      let localLevel = this.levelService.data[i];
      if(localLevel.linkedLevelIds.includes(levelId))
      {
        return localLevel;
      }
    }
  }

  /**
   * Restringe as posições dos pontos do mapa dentro dos limites válidos
   * Garante que nenhum ponto saia dos limites de 0-100% considerando o raio
   * @param map Mapa para aplicar as restrições
   */
  clampMap(map: Map)
  {
    const radius = this.userSettingsService.data.levelDiameter * 0.5
    const minRange = 0;
    const maxRange = 100;

    let points = map.points;
    let levels = this.levelService.filterByLocation(map.areaId);

    // Aplica restrições a todos os pontos dos níveis
    levels.forEach(level => {
      let point = points[level.id];

      // Restringe posição X
      if(point.position.xd >= maxRange)
      {
        point.position.xd = maxRange - radius;
      }
      else if(point.position.xd <= minRange)
      {
        point.position.xd = minRange + radius;
      }

      // Restringe posição Y
      if(point.position.yd >= maxRange)
      {
        point.position.yd = maxRange - radius;
      }
      else if(point.position.yd <= minRange)
      {
        point.position.yd = minRange + radius;
      }
    });

    // Salva as modificações
    this.mapService.modify(map);
  }

  /**
   * Cria um ponto no mapa para um nível específico
   * Se não for fornecida uma posição, calcula baseado no nível pai
   * @param map Mapa onde criar o ponto
   * @param levelid ID do nível
   * @param pointPosition Posição específica (opcional)
   */
  createMapPoint(map: Map, levelid:string, pointPosition?:LevelPoint)
  {
    // Encontra o nível pai para posicionamento relativo
    let parentLevel = this.getLevelParent(levelid);
    let parentLevelId = (parentLevel)? parentLevel.id : '';
    let parentLevelPosition = (parentLevelId)? map.points[parentLevelId].position : {xd:0,yd:0};

    // Calcula nova posição com offset do pai
    const offset = 2;
    let newLevelPosition = {xd: parentLevelPosition.xd + offset, yd: parentLevelPosition.yd + offset};

    // Cria o ponto com posição fornecida ou calculada
    map.points[levelid] = (pointPosition)? pointPosition : new LevelPoint(newLevelPosition);

    this.mapService.modify(map);
  }

  /**
   * Remove um ponto do mapa
   * @param map Mapa do qual remover o ponto
   * @param levelId ID do nível a ser removido
   */
  removeMapPoint(map: Map, levelId: string)
  {
    map.points[levelId] = undefined;
    this.mapService.modify(map);
  }

  /**
   * Define a proporção de aspecto do mapa
   * @param map Mapa para modificar
   * @param aspectRatio Nova proporção de aspecto
   */
  setMapAspectRatio(map:Map, aspectRatio:Ratio)
  {
    map.aspectRatio = aspectRatio;
    this.mapService.modify(map);
  }
}