import { AnchorPoint, ItemEvent } from '../../../../models/mapsys1'
import { Canvas } from './Canvas';

interface Position
{
    x : number;
    y : number;
}

export class SelectItem
{
    constructor(){}
    private allEventsOfLevel : ItemEvent[] = [];

    public selectItem(items : ItemEvent[], mousePosition : Position, itemCanvas : Canvas) : ItemEvent[]
    {
        this.allEventsOfLevel = [];
        let anchor : AnchorPoint = {xd : 0, yd: 0};

        for(let i = 0; i < items.length; i++)
        {
            // ✅ CORREÇÃO: Usa a mesma posição do drawItemOnCanvas para sincronizar área clicável
            anchor.xd = items[i].levelXPosition + 16.5;
            anchor.yd = items[i].levelYPosition + 1;
            let itemPosition : Position = itemCanvas.positionInPixels(anchor, false);

            // ✅ MELHORIA: Usa detecção circular mais precisa baseada no raio dinâmico
            // Calcula o raio dinâmico da mesma forma que o DrawItems
            const dynamicRadius = this.calculateDynamicRadius(itemCanvas);

            // Calcula distância do mouse ao centro do círculo
            const distance = Math.sqrt(
                Math.pow(mousePosition.x - itemPosition.x, 2) +
                Math.pow(mousePosition.y - itemPosition.y, 2)
            );

            // Verifica se o clique está dentro do círculo (com uma pequena margem de tolerância)
            if (distance <= dynamicRadius + 2) {
                this.allEventsOfLevel.push(items[i]);
            }
        }

        return this.allEventsOfLevel;
    }

    /**
     * Calcula o raio dinâmico para detecção de clique (mesmo cálculo do DrawItems)
     *
     * @param itemCanvas Canvas atual
     * @returns Raio calculado em pixels
     */
    private calculateDynamicRadius(itemCanvas: Canvas): number
    {
        const itemCircleBaseRadius = 5; // Mesmo valor padrão do DrawItems
        const itemCircleScaleFactor = 1.2; // Mesmo valor padrão do DrawItems
        let calculatedRadius = itemCircleBaseRadius;

        // === CÁLCULO BASEADO NO RAIO DOS CÍRCULOS DOS NÍVEIS ===
        if (itemCanvas.circleRadiusInPixels) {
            calculatedRadius = itemCanvas.circleRadiusInPixels * itemCircleScaleFactor;
        } else if (itemCanvas.levelRadius) {
            calculatedRadius = itemCanvas.levelRadius * itemCircleScaleFactor;
        }

        // === LIMITAÇÃO DE TAMANHO ===
        const minRadius = 3;
        const maxRadius = 18;

        return Math.max(minRadius, Math.min(maxRadius, Math.floor(calculatedRadius)));
    }
}