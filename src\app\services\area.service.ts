import { LevelService } from './level.service';
import { Injectable } from '@angular/core';
import { StaticService } from '../../templates/StaticService';
import { Area } from '../../models/mapsys1';

/**
 * Serviço para gerenciamento de áreas
 * Responsável por gerenciar áreas e seus níveis associados
 */
@Injectable({
  providedIn: 'root',
})
export class AreaService extends StaticService<Area>
{
  /** Array de níveis não ordenados */
  unsortedLevels : string [] = [];
  /** Flag para executar ordenação apenas uma vez */
  runOnce : boolean = true;

  /**
   * Construtor do serviço de áreas
   * @param _levelService Serviço de níveis injetado
   */
  constructor(private _levelService: LevelService)
  {
    super(Area.deepLoad, 'Area');
  }
  /**
   * Ordena os níveis de uma área específica
   * Reorganiza os IDs dos níveis na ordem correta
   * @param areaId ID da área para ordenar os níveis
   */
  public sortLevels(areaId: string): void
  {
    const area = this.findById(areaId);
    const levels = this._levelService.filterByIds(area.levelIds);
    area.levelIds = [];
    levels.forEach((level) =>
    {
      area.levelIds.push(level.id);
    });
    this.save();
  }

  /**
   * Obtém o índice de um nível específico
   * Retorna a posição do nível no array ordenado
   * @param levelId ID do nível para obter o índice
   * @returns Índice do nível no array
   */
  public getLevelIndex(levelId: string): number
  {
    if(this.runOnce)
    {
      this.runOnce = false;
      this.sortLevelArray(levelId);
    }

    let index = 0;
    for(let i = 0; i < this.unsortedLevels.length; i++)
    {
      if(this.unsortedLevels[i] == levelId)
      {
        index = i;
        break;
      }
    }
    return index;
  }

  /**
   * Ordena o array de níveis baseado no ID do nível
   * Popula o array unsortedLevels com os níveis da área
   * @param levelId ID do nível para determinar a área
   */
  sortLevelArray(levelId: string)
  {
    this.unsortedLevels = [];
    let splitId = levelId.split('.');

    let area = this.findById(splitId[0]);
    for(let i = 0; i < area.levelIds.length; i++)
    {
      let level = area.levelIds[i];
      this.unsortedLevels.push(level);
    }

    this.unsortedLevels = area.levelIds;
  }
}
